{"className": "com.longint.lomag.lomagweb.MainActivity", "classAnnotations": [], "interfaces": ["android.content.ComponentCallbacks", "android.content.ComponentCallbacks2", "android.view.KeyEvent$Callback", "android.view.LayoutInflater$Factory", "android.view.LayoutInflater$Factory2", "android.view.View$OnCreateContextMenuListener", "android.view.Window$Callback", "androidx.activity.OnBackPressedDispatcherOwner", "androidx.appcompat.app.ActionBarDrawerToggle$DelegateProvider", "androidx.appcompat.app.AppCompatCallback", "androidx.core.app.ActivityCompat$OnRequestPermissionsResultCallback", "androidx.core.app.ActivityCompat$RequestPermissionsRequestCodeValidator", "androidx.core.app.TaskStackBuilder$SupportParentable", "androidx.core.view.KeyEventDispatcher$Component", "androidx.lifecycle.LifecycleOwner", "androidx.lifecycle.ViewModelStoreOwner", "androidx.savedstate.SavedStateRegistryOwner", "com.longint.lomag.lomagweb.db.connect.ConnectionListener", "com.longint.lomag.lomagweb.db.resultAsyncTasks.GetResultInterface", "com.longint.lomag.lomagweb.db.resultAsyncTasks.ProcedureExecutionAsyncTask$ProcedureExecutionListener", "com.longint.lomag.lomagweb.fragments.AddDocumentElementFragment$AddDocumentElementFragmentListener", "com.longint.lomag.lomagweb.fragments.AddDocumentElementMultiLocationFragment$AddDocumentElementMultiLocationFragmentListener", "com.longint.lomag.lomagweb.fragments.AddInterbranchTransferElementFragment$AddInterbranchTransferElementFragmentListener", "com.longint.lomag.lomagweb.fragments.AddInterbranchTransferSerialNrElementFragment$AddInterbranchTransferSerialNrElementFragmentListener", "com.longint.lomag.lomagweb.fragments.AddSerialNumbersFragment$AddSerialFragmentListener", "com.longint.lomag.lomagweb.fragments.AvailableSerialsFragment$AvailableSerialsFragmentListener", "com.longint.lomag.lomagweb.fragments.CorrectionInvoiceListFragment$CorrectionInvoiceListListener", "com.longint.lomag.lomagweb.fragments.DocumentsPreviewFragment$DocumentsFragmentListener", "com.longint.lomag.lomagweb.fragments.HostListFragment$HostListFragmentFragmentListener", "com.longint.lomag.lomagweb.fragments.InventorySummaryFragment$InventorySummaryFragmentListener", "com.longint.lomag.lomagweb.fragments.LoginFragment$LoginFragmentListener", "com.longint.lomag.lomagweb.fragments.MainMenuFragment$MainMenuFragmentListener", "com.longint.lomag.lomagweb.fragments.NetworkSettingsFragment$NetworkSettingsFragmentListener", "com.longint.lomag.lomagweb.fragments.NewDocumentFragment$NewDocumentFragmentListener", "com.longint.lomag.lomagweb.fragments.NewInvoiceArticlesFragment$NewInvoiceArticlesFragmentListener", "com.longint.lomag.lomagweb.fragments.NewInvoiceFragment$NewInvoiceFragmentListener", "com.longint.lomag.lomagweb.fragments.NewLocationDialogFragment$NewLocationDialogFragmentListener", "com.longint.lomag.lomagweb.fragments.NewOrderArticlesFragment$NewOrderArticlesFragmentListener", "com.longint.lomag.lomagweb.fragments.NewOrderFragment$NewOrderFragmentListener", "com.longint.lomag.lomagweb.fragments.NewRentalArticlesFragment$NewRentalArticlesFragmentListener", "com.longint.lomag.lomagweb.fragments.NewRentalFragment$NewRentalFragmentListener", "com.longint.lomag.lomagweb.fragments.NewReturn_RentalArticlesFragment$NewReturn_RentalArticlesFragmentListener", "com.longint.lomag.lomagweb.fragments.NewReturn_RentalFragment$NewReturn_RentalFragmentListener", "com.longint.lomag.lomagweb.fragments.NewUnitDialogFragment$NewUnitDialogFragmentListener", "com.longint.lomag.lomagweb.fragments.SerialNumberDedicatedColumnsFragment$SerialNumberDedicatedColumnsListener", "com.longint.lomag.lomagweb.fragments.StartUpScreenFragment$StartUpScreenFragmentListener", "com.longint.lomag.lomagweb.fragments.WarehouseActualPreviewFragment$ActualStockFragmentListener", "com.longint.lomag.lomagweb.fragments.WarehouseItemsNotOnInvFragment$WarehouseItemsNotOnInventoryFragmentListener", "com.longint.lomag.lomagweb.fragments.WarehouseItemsToRemoveFragment$WarehouseItemsToRemoveFragmentListener", "com.longint.lomag.lomagweb.fragments.WarehousePreviewFragment$ActualStockFragmentListener", "com.longint.lomag.lomagweb.fragments.WarehouseSelectionFragment$WarehouseSelectionFragmentListener", "com.longint.lomag.lomagweb.fragments.WaresForDocumentPreviewFragment$WaresForDocumentPreviewFragmentListener"], "superClasses": ["androidx.appcompat.app.AppCompatActivity", "androidx.fragment.app.FragmentActivity", "androidx.activity.ComponentActivity", "androidx.core.app.ComponentActivity", "android.app.Activity", "android.view.ContextThemeWrapper", "android.content.ContextWrapper", "android.content.Context", "java.lang.Object"]}