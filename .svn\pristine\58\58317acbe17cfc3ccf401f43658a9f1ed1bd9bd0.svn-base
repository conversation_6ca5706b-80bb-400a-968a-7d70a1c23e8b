package com.longint.lomag.lomagweb.db.toobjects;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;

/**
 * Created by <PERSON><PERSON> on 2016-06-11.
 */
public class SerialNumber implements DBObject, Serializable {

    public static final String TABLE_NAME = "dbo.SerialNumbers";
    public static final String ID_NAME = "IDSerialNumber";
    public static final String DOC_EL_ID_NAME = "IDElementuRuchuMagazynowego";
    public static final String SERIAL_NR_NAME = "Number";


    private int id;
    private int docElementId;
    private String serialNr;
    private Date validationDate;

    // Dedicated columns data
    private HashMap<String, String> dedicatedColumnsData = new HashMap<>();

    public String getSerialNr() {
        return serialNr;
    }

    public void setSerialNr(String serialNr) {
        this.serialNr = serialNr;
    }

    public int getDocElementId() {
        return docElementId;
    }

    public void setDocElementId(int docElementId) {
        this.docElementId = docElementId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Date getValidationDate() {
        return validationDate;
    }

    public void setValidationDate(Date validationDate) {
        this.validationDate = validationDate;
    }

    public HashMap<String, String> getDedicatedColumnsData() {
        return dedicatedColumnsData;
    }

    public void setDedicatedColumnValue(String columnName, String value) {
        if (dedicatedColumnsData == null) {
            dedicatedColumnsData = new HashMap<>();
        }
        dedicatedColumnsData.put(columnName, value);
    }

    public String toString()
    {
        return serialNr;
    }

    @Override
    public boolean equals(Object o) {
        return serialNr.equals(((SerialNumber)o).getSerialNr());
    }

    @Override
    public int hashCode() {
        return serialNr.hashCode();
    }
}
