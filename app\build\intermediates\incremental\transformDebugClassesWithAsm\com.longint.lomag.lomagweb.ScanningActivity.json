{"className": "com.longint.lomag.lomagweb.ScanningActivity", "classAnnotations": [], "interfaces": ["android.content.ComponentCallbacks", "android.content.ComponentCallbacks2", "android.view.KeyEvent$Callback", "android.view.LayoutInflater$Factory", "android.view.LayoutInflater$Factory2", "android.view.SurfaceHolder$Callback", "android.view.View$OnCreateContextMenuListener", "android.view.Window$Callback", "androidx.activity.OnBackPressedDispatcherOwner", "androidx.appcompat.app.ActionBarDrawerToggle$DelegateProvider", "androidx.appcompat.app.AppCompatCallback", "androidx.core.app.ActivityCompat$OnRequestPermissionsResultCallback", "androidx.core.app.ActivityCompat$RequestPermissionsRequestCodeValidator", "androidx.core.app.TaskStackBuilder$SupportParentable", "androidx.core.view.KeyEventDispatcher$Component", "androidx.lifecycle.LifecycleOwner", "androidx.lifecycle.ViewModelStoreOwner", "androidx.savedstate.SavedStateRegistryOwner"], "superClasses": ["jim.h.common.android.lib.zxing.CaptureActivity", "androidx.appcompat.app.AppCompatActivity", "androidx.fragment.app.FragmentActivity", "androidx.activity.ComponentActivity", "androidx.core.app.ComponentActivity", "android.app.Activity", "android.view.ContextThemeWrapper", "android.content.ContextWrapper", "android.content.Context", "java.lang.Object"]}