<?xml version="1.0" encoding="utf-8"?>
<resources
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingTranslation" >
    <string name="app_name">LoMag for MS SQL DB</string>
    <string name="hello_world">Hello world!</string>
    <string name="action_settings">Settings</string>
    <string name="app_folder_name">LomagAppLogs</string>


    <string name="startup_screen">Startup screen</string>
    <string name="hide_this_screen">Hide this screen</string>
    <string name="connecting_to_database">Connecting to database...</string>
    <string name="wrong_format">Wrong format. Scan code in LoMag windows menu settings/networking.</string>
    <string name="scan_settings">Scan Settings</string>
    <string name="rented_title">Rented:</string>
    <string name="net_amount">Net Amount:</string>
    <string name="gross_amount">Gross Amount:</string>
    <string name="rental_title">Rental</string>
    <string name="return_rental_title">Return Rental</string>
    <string name="rental_date">Rental date:</string>
    <string name="return_date">Return date:</string>
    <string name="rental_status">Rental Status:</string>
    <string name="settlement_unit">Settlement Unit:</string>
    <string name="net_deposit">Net Deposit:</string>
    <string name="gross_deposit">Gross Deposit:</string>
    <string name="current_stock_title">Current stock:</string>
    <string name="returned_amount_exceeds">Returned quantity exceeds the quantity rented.</string>
    <string name="rental_amount_exceeds">Rental quantity exceeds the Current stock.</string>
    <string name="the_rental_date_earlier_than_return_date">The rental date must be earlier than the return date.</string>
    <string name="no_item_to_return">No item to return.</string>

    <string name="search_by_dedicated_field">Search by Dedicated Column</string>
    <string name="serial_number_dedicated_columns">Serial Number Dedicated Columns</string>
    <string name="search_by_serial_no">Search by Serial No</string>
    <string name="search_by_receipt">Search by Receipt</string>
    <string name="search_by_item">Search by Item</string>
    <string name="search_filter">Filter</string>
    <string name="search_today">from today</string>
    <string name="search_last7day">last 7 days</string>
    <string name="search_thismounth">this month</string>
    <string name="search_lastmounth">from previous month</string>
    <string name="search_thisyear">this year</string>
    <string name="search_all">all</string>
    <string name="search_by_location">Search By Location</string>
    <string name="do_you_want_save_correction_invoice">Do you want to save the Correction Invoice?</string>
    <string name="no_data_found">No data found.</string>

    <string name="date_of_issue_rental">Date of issue:</string>
    <string name="date_colon">Date:</string>
    <string name="supplier_colon">Supplier:</string>
    <string name="save_invoiceLine">Save invoice line</string>
    <string name="add_invoiceLine">Add InvoiceLine</string>
    <string name="number_short_colon">No:</string>
    <string name="source_invoice">Source invoice:</string>
    <string name="select_invoiceline">Select InvoiceLine</string>
    <string name="select_invoice">Select Invoice</string>
    <string name="date_of_sale_title">Date of sale:</string>
    <string name="date_of_issue_title">Date of issue:</string>
    <string name="payment_deadline">Payment deadline:</string>
    <string name="pro_forma_title">Pro Forma</string>
    <string name="invoice_order">Invoice</string>
    <string name="correction_invoice">Correction Invoice</string>
    <string name="select_invoice_algorithm">Select Invoice Algorithm</string>
    <string name="please_choose_algorithm_invoice">Please choose an algorithm for calculating invoice value:</string>
    <string name="gross_invoice">Gross Invoice</string>
    <string name="net_invoice">Net Invoice</string>

    <string name="do_you_want_stop_editing">Do you want to stop editing?</string>
    <string name="Document_with_future_date">Document with a future date.</string>
    <string name="invoice_number_distorted_invoice_later_date">Invoice number ordering will be distorted because there is an invoice issued with a later date!</string>
    <string name="price_times_quantity_lower_bound">Price, quantity and their item must be greater than zero.</string>
    <string name="net_price">Net price:</string>
    <string name="net_price_label">Net price: %s %s</string>
    <string name="net_value">Net value:</string>
    <string name="gross_value">Gross value:</string>
    <string name="gross_price">Gross price:</string>
    <string name="gross_price_label">Gross price: %s %s</string>
    <string name="purchase_price">Purchase price:</string>
    <string name="payment_type">Type of payment:</string>
    <string name="order_status">Status:</string>
    <string name="Offer_title">Offer</string>
    <string name="customer_order">Customer order</string>
    <string name="supplier_order">Supplier order</string>
    <string name="select_option">Please select value</string>



    <!-- Network setting -->
    <string name="network_settings">Network settings</string>
    <string name="server_name">Server name:</string>
    <string name="password">Password:</string>
    <string name="user">User:</string>
    <string name="db_name">Database name:</string>
    <string name="server_name_hint">Server name</string>
    <string name="password_hint">Password</string>
    <string name="user_hint">User</string>
    <string name="db_name_hint">Database name</string>
    <string name="save_settings">Save settings</string>
    <string name="no_server_name">Server name was not specified</string>
    <string name="sql_instance_name">SQL instance name:</string>
    <string name="port_nr">Port number:</string>
    <string name="port_nr_hint">Port number</string>
    <string name="sql_instance_name_hint">SQL instance name</string>
    <string name="db_name_error">Database name was not specified</string>
    <string name="user_error">User was not specified</string>
    <string name="password_error">Password was not specified</string>
    <string name="connect">Connect</string>
    <string name="info">To use this application install LoMag for Windows and choose "This computer is a server" in network settings. Remember computer name and write it above in field "server name".</string>
    <string name="network_settings_name">Microsoft SQL server data</string>
    <string name="host_search">Searching devices...</string>

    <!-- Network settings defoult data-->
 <string name="server_name_default">IP_or_NAME</string>
    <!--<string name="server_name_default">*************</string>-->
    <string name="user_default">sa</string>
    <string name="pass_default">magazyn2008</string>
    <string name="pass_default_2">mAgAzyn_2008!</string>
    <!--<string name="warehouse_default">ProgramMagazynowy</string>-->
    <string name="warehouse_default">ProgramMagazynowy</string>
    <string name="instance_default">SQLEXPRESS</string>
    <string name="port_default">1433</string>
    <string name="version">Version: %s (%s)</string>

    <!--Login-->
    <string name="login">Login:</string>
    <string name="logging_in">Logging in</string>
    <string name="wrong_user">Wrong username or password!</string>
    
    <!-- errors -->
    <string name="error">Error</string>
    <string name="sorry">Sorry...!</string>
    <string name="app_crashed">%1$s: Your App crashed! Fix it!</string>
    <string name="report">Report</string>
    <string name="sorry_app_crashed">Oops, Your application has crashed</string>



    <!-- Warehouse selection -->
    <string name="warehouse_selection">Warehouse selection</string>
    <string name="selected_warehouse_content_discribtion">selected warehouse</string>
    <string name="no_warehouse">No warehouse in selected Database. Please connect to a different database</string>
    
    <!-- Main menu -->
	<string name="actual_state">Stock levels</string>
	<string name="add_stock_item">Add stock item</string>
	<string name="remove_stock_item">Remove stock item</string>
    <string name="receive">Receipt</string>
    <string name="release">Issue</string>
    <string name="inventarisation">Inventory</string>
    <string name="inventory_sheet_title">Inventory sheet</string>
    <string name="order_picking_title">Order picking</string>
    <string name="order_picking2_title">Collection of rentals</string>
    <string name="change_location_title">Change location</string>
    <string name="quantity_edit">Edition</string>
    <string name="internal_goods_pw">Internal receipt</string>
    <string name="internal_goods_rw">Internal issue</string>
    <string name="interbranch_transfer">Interbranch transfer</string>
    <string name="documents">Documents</string>
    <string name="program_settings">Settings</string>
    <string name="wares_of_document">Items on document</string>

    <!-- Add item -->
    <string name="barcode">Barcode:</string>
    <string name="barcode2">Barcode:</string>
    <string name="barcode_hint">Barcode</string>
    <string name="item_name">Item name:</string>
    <string name="count_order">Quantity ordered:</string>
    <string name="count_pick">Completed quantity:</string>
    <string name="quantity_in_doc">Item quantity in doc:</string>
    <string name="item_name_hint">Item name</string>
    <string name="empty_item_name">Item name cannot be empty!</string>
    <string name="empty_barocode">Barcode cannot be empty!</string>
    <string name="group">Item groups:</string>
    <string name="group_hint">Item groups</string>
    <string name="min_state">Low Qty Threshold:</string>
    <string name="max_state">High Qty Threshold:</string>
    <string name="vat_rate">VAT rate:</string>
    <string name="default_supplier">Default supplier:</string>
    <string name="vat_rate_hint">VAT rate</string>
    <string name="remarks">Remarks:</string>
    <string name="remarks_hint">Remarks</string>
    <string name="photo">Photo:</string>
    <string name="photo_hint">Photo</string>
    <string name="unit">Unit:</string>
    <string name="unit_hint">Unit</string>
    <string name="add">Add</string>
    <string name="save">Save</string>
    <string name="edit_stock_item">Edit item</string>
    <string name="preview_stock_item">Preview item</string>
    <string name="state">State:</string>

    <!-- Remove item-->
    <string name="no_itmes">No warehouse items in selected Database.</string>
    <string name="remove_item">Remove item %s?</string>

    <!-- Stock item-->
    <string name="price">Price:</string>
    <string name="delivery">Delivery:</string>
    <string name="count">Quantity:</string>
    <string name="price_hint">Price</string>
    <string name="count_hint">Quantity</string>

    <!-- add document  -->
    <string name="document_nr">Document number:</string>
    <string name="order_nr">Order number:</string>
    <string name="add_items">Add items</string>
    <string name="refill_items">Refill items</string>
    <string name="contact_person">Account:</string>
    <string name="contact_person_error">There is no account with such name</string>
    <string name="nr_exists">There exists a document with such name already</string>
    <string name="documents_date">Document date:</string>
    <string name="from_warehouse">From warehouse:</string>
    <string name="to_warehouse">To warehouse:</string>
    <string name="target_warehouse_error">Target warehouse cannot be the same warehouse</string>

    <!-- Add document element -->
    <string name="insert_next">Add next</string>
    <string name="save_and_exit">Save and exit</string>
    <string name="value">Value:</string>
    <string name="actual_state_label">Current stock level:</string>
    <string name="add_doc_element">Add warehouse movement item</string>
    <string name="doc_element_added">document element added</string>
    <string name="no_item_with_selected_code">There is no item with barcode: %1s. Do you want to add the outstanding item to the program?</string>
    <string name="no_item_with_selected_name">There is no item with name: %1s. Do you want to add the outstanding item to the program?</string>
    <string name="item_in_archive">Selected item is in the archive. Do you want to restore from archive?</string>
    <string name="wrong_barcode">There is no item with given barcode</string>
    <string name="archival_item">Item is archive</string>
    <string name="wrong_name">There is no item with given name</string>
    <string name="left_on_stock">Left on stock:</string>
    <string name="warehouse_localization">Warehouse location:</string>
    <string name="warehouse_localization_source">Warehouse location source:</string>
    <string name="warehouse_localization_destination">Warehouse location destination:</string>
    <string name="add_warehouse_localization">Add warehouse location</string>
    <string name="location_description">Location description:</string>
    <string name="location_code">Location code:</string>
    <string name="archive_location">Archive location</string>
    <string name="empty_location_code">Location code connot be empty</string>
    <string name="location_code_error">Not enough items in location %1s.</string>
    <string name="location_exists">This code is already ascribed to a location.</string>
    <string name="location_code_no_loc_error">location with the given name does not exist</string>
    <string name="order_picking_count">Item on order</string>
    <string name="order_picking_count2">Completed items</string>
    <string name="doc_load_count">First %s of documents loaded, enter the document number in the search field</string>
    <string name="change_location_error">It is not possible to perform a transfer where the packing slip is the current MM transfer document</string>
    <string name="change_location_multi_error">A circular shift cannot be performed</string>
    <string name="order_picking_error">Select an order for picking</string>
    <string name="location_code_must_diff">Locations must be different</string>
    <string name="location_must_select">Locations must be selected</string>
    <string name="select_location_no_item">There are no deliveries to be transferred in the selected location</string>
    <string name="receive_title">Receipt</string>
    <string name="internal_goods_pw_title">Internal receipt</string>
    <string name="internal_goods_rw_title">Internal issue</string>
    <string name="release_title">Issue</string>
    <string name="inv_title">Inventory</string>
    <string name="delivery_items_preview_label">Preview of receipts:</string>
    <string name="items_preview_label">Preview of items:</string>
    <string name="price_times_quantity_upper_bound">Price, quantity and their item have to be less than or equal to 99999999999</string>
    <string name="min_state_upper_bound">Low Qty Threshold has to be less than or equal to 99999999999</string>
    <string name="max_state_upper_bound">High Qty Threshold has to be less than or equal to 99999999999</string>
    <string name="stock_level_error">Stock level is smaller</string>
    <string name="stock_gs1_error_kg">Unable to read quantity from GS1 code. Change the unit of item to kg</string>
    <string name="stock_gs1_error_szt">Unable to read quantity from GS1 code. Change item unit to pcs</string>
    <string name="copy_item_to_warehouse_question">This item does not exist in this warehouse yet. Do you want to copy the item file?</string>
    <string name="target_location">Target location</string>

    <!-- document -->
    <string name="document_name">Document No:</string>
    <string name="contractor_label">Contractor:</string>
   <string name="doc_value_label">Value:</string>
   <string name="comments_label">comments:</string>

    <!-- inventory -->
    <string name="remanent_question">The inventory is missing %1$d items. Select one of the following options:</string>
    <string name="remanent_question_show">Show items which are missing from the inventory</string>
    <string name="remanent_question_actual">Add the missing items with their current stock level</string>
    <string name="remanent_question_zero">Add the missing items with their stock level at zero</string>
    <string name="remanent_conf">INVENTORY CONFIRMATION \n This operation will change the stock levels. Do you wish to save the inventory?</string>
    <string name="remanent_changes">Changes introduced:</string>
    <string name="goods">Item:</string>
    <string name="value_summary">Total \nvalue:</string>
    <string name="quantity">Quantity:</string>
    <string name="old_quantity">Old:</string>
    <string name="new_quantity">New:</string>
    <string name="summary">Total:</string>
    <string name="not_used_elements">Missing elements:</string>
    <string name="back_to_add">Return to adding items</string>
    <string name="accept_changes">Save changes</string>
    <string name="cancel_changes">Delete inventory</string>
    <string name="missing_element">Missing items:</string>
    <string name="inv_summary">Inventory summary</string>
    <string name="correction_remarks">Correction created automatically by inventory</string>

    <!-- Settings -->


    <string name="show_connection_screen">Show connection screen</string>
    <string name="hide_connection_screen">Hide connection screen</string>
    <string name="include_price">Prices support</string>
    <string name="no_scan_mode">Hardware scanner or manual</string>
    <string name="auto_scan">Removed unused units</string>
    <string name="remove_units">Remove unused units</string>
    <string name="scan_only">Choose codes to scan</string>
    <string name="count_on_remove">Quantity on issue</string>
    <string name="serial_number_support">Support for serial numbers</string>
    <string name="photo_size">Images size</string>

    <string-array name="listItemsOnRemove">
        <item>0</item>
        <item>1</item>
        <item>stock</item>
    </string-array>

    <string-array name="photo_sizes">
        <item>small</item>
        <item>medium</item>
        <item>big</item>
    </string-array>

    <string name="keyboardShortcutsTitle">Keyboard shortcuts</string>
    <string name="insertNextShortcutTitle">A shortcut for \'Add next\'</string>
    <string name="insertSNShortcutTitle">A shortcut for \'Add S\N\'</string>
    <string name="saveAndExitShortcutTitle">A shortcut for \'Save and exit\'</string>
    <string name="addItemsShortcutTitle">A shortcut for \'Add items\'</string>
    <string name="saveShortcutTitle">A shortcut for \'Save\'</string>
    <string name="duplicatedKeyError">You cannot assign a key to several buttons</string>

    <string-array name="keyboardShortcuts">
        <item>None</item>
        <item>F1</item>
        <item>F2</item>
        <item>F3</item>
        <item>F4</item>
        <item>F5</item>
        <item>F6</item>
        <item>F7</item>
        <item>F8</item>
        <item>F9</item>
        <item>F10</item>
        <item>F11</item>
        <item>F12</item>
    </string-array>

    <!-- code types -->
    <string name="qr_code">QR CODE</string>
    <string name="data_matrix">DATAMATRIX</string>
    <string name="upce">UPC E</string>
    <string name="upca">UPC A</string>
    <string name="ean8">EAN 8</string>
    <string name="ean13">EAN 13</string>
    <string name="code128">CODE 128</string>
    <string name="code39">CODE 39</string>
    <string name="code93">CODE 93</string>
    <string name="itf">ITF</string>
    <string name="pdf417">PDF 417</string>

    <!-- Scanning-->
    <string name="scan_info">Scan barcode</string>


    <!-- Serial numbers -->
    <string name="serial_nrs">Serial numbers for item</string>
    <string name="remaining_serials">Serial numbers remaining to be entered: %d</string>
    <string name="serial_nr">Serial number:</string>
    <string name="doc_type">Document type:</string>
    <string name="serial_nr_for_current">Serial numbers for current item:</string>
    <string name="skikp_sn_sel">Skip S/N selection</string>
    <string name="serial_in_use">Serial number \'%s\' already exists. Enter another number.</string>
    <string name="add_auto">Assign automatically</string>
    <string name="no_serial">Given serial nr doesn\'t exist on any added delivery</string>
    <string name="maximum_condition_exceeded">Maximum condition exceeded</string>
    <string name="other_location_error">Shipments from multiple locations cannot be selected</string>
    <string name="min_condition_delivery_exceeded">Select quantity on delivery</string>
    <string name="min_condition_exceeded">Select quantity</string>
    <string name="quantity_condition_exceeded">Quantity cannot be a fractional number due to handling of serial numbers</string>
    <string name="available_serials">Available serial numbers</string>
    <string name="serials">serial numbers</string>
    <string name="set_as_serial">Should you enable serial number support for an item and generate serial numbers for historical warehouse transfers?</string>
    <string name="set_picking_ok">All items from the order %s have been completed at WZ %s</string>
    <string name="set_picking_bad">%s items from order %s have not been completed</string>
    <string name="set_picking_save">Do you want to save?</string>
    <string name="archival">archival</string>
    <string name="product">product</string>
    <string name="service">service</string>
    <string name="set_as_service">ATTENTION, a change of item to a service will reset their stock levels. Are you sure you want to do this?</string>
    <string name="quantity_must_be_int">Due to serial number support, the amount cannot be a fractional number.</string>
    <string name="serials_changed">Serial number %s already exists. It was problably added a moment ago from different device. Please enter serial numbers again</string>
    <string name="serials_dont_exist">Serial number %s doesn\'t exist. It was probably removed from different device, a moment ago. Please choose serial numbers again</string>
    <string name="serial_numbers_mismatch">The transfer is not possible because there are no serial numbers in one of the warehouses</string>
    <string name="serial_nr_add">Add S/N</string>
    <string name="serial_nr_has_add">S/N has been added</string>
    <string name="add_multi_localization">Location</string>
    <string name="serial_nr_added_successfully"> New item %s added successfully</string>
    <string name="serial_nr_no_serial">No item with the given S/N</string>
    <string name="dedicated_column_mandatory_error">Brak uzupełnionej wartości kolumny dedykowanej %1$s dla numeru seryjnego %2$s towaru %3$s</string>
    <string name="serial_numbers_preview">Serial Numbers Preview</string>
    <string name="serial_numbers">Serial Numbers</string>


    <!-- Series and expiration date-->
    <string name="expiration_date">Expiration date:</string>
    <string name="lot_number">Lot number:</string>
    <string name="lot_number_not_fount">Serial number not found</string>
    <string name="select_date">Select date:</string>
    <string name="quantity_greater_than_in_order">quantity greater than in order</string>
    <string name="quantity_greater_than_in_location">quantity greater than in selected location</string>

	<!-- Dialogs -->
	<string name="connection_lost">Connection to database was lost. Check your internet connection.</string>
	<string name="internet_connection_failed">Internet connection failed</string>
	<string name="check_internet_connection">Please check Your internet connection.</string>
	<string name="connection_to_database_failed">Connection with database failed</string>
	<string name="no_connection_with_server">Given server %1s doesn\'t answer.</string>
    <string name="no_connection">Couldn\'t connect to database</string>
    <string name="try_again">TRY AGAIN</string>
	<string name="settings">Settings</string>
    <string name="new_unit">New meassure unit:</string>
    <string name="empty_unit_error">New meassure unit cannot be empty</string>
    <string name="code_already_exists">There exist already an Item %1s with the same code in warehouse. Create another item with the same code?</string>
    <string name="items_not_removed">Some items where not removed because they exist on documents.</string>
    <string name="exists_on_inv">Actual document contains the selected item already. Do you want to replace it?</string>
    <string name="wrong_db_ver">Database version must be greater then %s.</string>
    <string name="no_tables">Some LoMag tables are missing. Please use the version for Windows to create the missing tables.</string>
    <string name="no_local_connection">the phone is not connected to a local network.</string>
    <string name="document_deletion_confirmation">Do you want to delete document with all items?</string>
    <string name="document_deletion_fail">A document with related documents cannot be deleted</string>

    <!-- Toasts -->
    <string name="item_added">Item added</string>
    <string name="items_removed">Items removed</string>
    <string name="correction_in_progress">Adding correction in progress...</string>
    <string name="correction_added">Correction added...</string>
    <string name="no_items_to_remove">No items to remove</string>

	<!-- action bar menu -->
	<string name="menu_connection_settings">Connection settings</string>
	<string name="menu_warehouse_selection">Warehouse selection</string>
	
	<!-- Other -->
	<string name="cancel">Cancel</string>
	<string name="ok">OK</string>
    <string name="yes">YES</string>
    <string name="no">NO</string>
    <string name="account_mandatory_error">Contractor is required</string>

    <string name = "new_account_save">Save</string>
    <string name = "new_account_name">Account name:</string>
    <string name = "new_account_name_cannot_be_empty">Account name cannot be empty!</string>
    <string name = "new_account_customer">Customer</string>
    <string name = "new_account_supplier">Supplier</string>
    <string name = "new_account_employee">Employee</string>
    <string name = "new_account_no_account_type">An account must be a supplier, customer or employee. You cannot leave all the fieleds empty.</string>
    <string name = "new_account_account_address">Account address</string>
    <string name = "new_account_street_and_number">Street and number:</string>
    <string name = "new_account_town">Town:</string>
    <string name = "new_account_postcode">Postcode:</string>
    <string name = "new_account_country">Country:</string>
    <string name = "new_account_contact_data">Contact data</string>
    <string name = "new_account_contact_person">Contact person:</string>
    <string name = "new_account_phone">Phone:</string>
    <string name = "new_account_fax">Fax:</string>
    <string name = "new_account_www_address">WWW address:</string>
    <string name = "new_account_email">e-mail:</string>
    <string name = "new_account_additional_information">Additional information</string>
    <string name = "new_account_tin">TIN:</string>
    <string name = "new_account_business_registry_number">Business Registry Number:</string>
    <string name = "new_account_remarks">Remarks:</string>
    <string name = "new_account_account_exist">An account %1$s already exists in the database. Do you want to add another account with the same name?</string>
    <string name = "new_account_tin_exist">An account with this Tax Identification Number %1$s already exists in the database. Do you want to add another account with the same Tax Identification Number?</string>

    <string name = "some_items_were_not_removed">Some items were not removed</string>
    <string name = "unknown_target_warehouse">An unknown target warehouse</string>
</resources>
