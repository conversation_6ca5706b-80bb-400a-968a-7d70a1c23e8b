-- Merging decision tree log ---
manifest
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:2:1-83:12
MERGED from [androidx.preference:preference:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\6b002b77efe2d635d59dd9c547adbb36\transformed\preference-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\fb819667f8bcab372a213564cd272f5f\transformed\material-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\vs\gradle-5.6.4\caches\transforms-3\7bc85073c6f0e7e360d25c1b5cf33d74\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-19:12
MERGED from [com.github.Kunzisoft:Android-SwitchDateTimePicker:2.0] C:\vs\gradle-5.6.4\caches\transforms-3\8d8c3637d7a919dae0be65409af292c7\transformed\jetified-Android-SwitchDateTimePicker-2.0\AndroidManifest.xml:2:1-16:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\vs\gradle-5.6.4\caches\transforms-3\0b5730c5530219111c6c8038fb60e71b\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.prolificinteractive:material-calendarview:1.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\a122cc92abf2571993a259f471130e57\transformed\jetified-material-calendarview-1.5.0\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ec3bba6edc67982b9dd618e402bc1bba\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ab2d04c3152c165246bd50fb85414d1e\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.multidex:multidex:2.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\19e9e6f3f1799c62d257af65fc04d712\transformed\multidex-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-analytics:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\4b3fbc7e49d384c6073427e2516ed6b9\transformed\jetified-firebase-analytics-17.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:2:1-22:12
MERGED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:17:1-40:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\f05ca3f7f3ac99fb02f0a43f29cc7576\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\5acc49254da2eecabd04d20f979c0388\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:17:1-38:12
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:17:1-47:12
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:17:1-36:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\e2c2a4e457b826af7f59b494c890816b\transformed\jetified-play-services-measurement-sdk-17.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\37af93bade8d2bcbb1527bb6a06377be\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\4bff1c84b3c6a5aef74c09b3e29059f0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\48dc1825e3e42dee655a22d56a1235b5\transformed\transition-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d7b520c337af544206a88043ea327e17\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-iid-interop:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\bfc0f35061496c8167916438f98b9e0d\transformed\jetified-firebase-iid-interop-17.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\011e0e91d27ddd63530d827ec14f33c0\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\18f9df407a0431d546c69bbf640b58e5\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:17:1-29:12
MERGED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:2:1-19:12
MERGED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-installations-interop:16.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ce9e8986e59fd54553e07493d9a2fd93\transformed\jetified-firebase-installations-interop-16.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.gms:play-services-tasks:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ce900338fda5ad5341b0e1f2d4755728\transformed\jetified-play-services-tasks-17.0.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\468a4af3c6ac6cabd16f38e0203f1db7\transformed\jetified-firebase-measurement-connector-18.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d5b1e7a25eaad23beac6984d0051fad9\transformed\jetified-play-services-ads-identifier-17.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\bdcd78434eefd8a81213a729478dce7e\transformed\jetified-play-services-measurement-sdk-api-17.5.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-base:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\7f363057f60eaf9c9fc40a70cd970db5\transformed\jetified-play-services-measurement-base-17.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.fragment:fragment:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\9a67fc43e4892495db6a69d59ca55315\transformed\fragment-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\c34e35acf4c8880e27899cc1430ce44a\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\1714e6670f8230ea9a01fb5649cf2a69\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\72aefe36bf2238cc0bb2b823f58f4a35\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\0d5c89cb173b24a90424b6ea6099597c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\449eba516b9a2ec0573a5f6ef09f9f2b\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d0ff6ec68c203861eb4eabe2859e8162\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\1efe95b7d94cae64b87f2cb1b9cf468e\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\dff4fa6ad7f91ac2faf8765f429d526c\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\vs\gradle-5.6.4\caches\transforms-3\67fb4ad2733e1d0fc0f4bc484fca152c\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\b4a6aaf082da9b1cbfea42e8548f0e3f\transformed\jetified-activity-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\55031aa06c25f8cc72da74c1dbe4285b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\920ab1b4d2e34f305e98b5b500f149e4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\0dae37a976ed46f7b871d598b1d13ce3\transformed\core-1.3.2\AndroidManifest.xml:17:1-26:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-components:16.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\f1964c3bf13626cc6eeb7d737e73d38f\transformed\jetified-firebase-components-16.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-encoders-json:16.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\b86ae37eaeaf6f6bc6564c89cbb7b712\transformed\jetified-firebase-encoders-json-16.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\6d9a378dd87f9a1873292bc036387f09\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\e67690560cf8471bb54b6e9f02910e5e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\c9a14066f66d5cc9afca129a51f6a3f0\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\7dfa5acbff5291c2e54a16ad3a753b3a\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\95476d54cc1a6555a8ab0b460dca0d58\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\005203ed722122e053e9b8e1ae4ef084\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\2e9433368b23004bc3e0c1ecacbc6e74\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\a2ed1a288c8cd6f2ef584c97cf68a350\transformed\lifecycle-livedata-core-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d4b9ad3bf9a35e8b37b46241d826f1c4\transformed\core-runtime-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\e9ceaa3a6d9d57dedc51e43f776427b6\transformed\lifecycle-runtime-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\adb944c16195823e129769fab694bd18\transformed\lifecycle-viewmodel-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\e66c1242e4f8915f12bb40c95a0b142a\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.datatransport:transport-api:2.2.0] C:\vs\gradle-5.6.4\caches\transforms-3\f05b285998803e22de438107db46e04c\transformed\jetified-transport-api-2.2.0\AndroidManifest.xml:15:1-22:12
INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:2:1-83:12
	package
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:3:5-41
		INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
		INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:2:1-83:12
		INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:4:5-51
	android:versionCode
		INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:2:1-83:12
		INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:2:11-69
	android:installLocation
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:6:5-45
uses-feature#android.hardware.camera
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:8:5-85
	android:required
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:8:58-82
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:8:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:9:5-95
	android:required
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:9:68-92
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:9:19-67
uses-permission#android.permission.INTERNET
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:10:5-67
MERGED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:10:5-67
MERGED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:26:5-67
MERGED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:26:5-67
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\bdcd78434eefd8a81213a729478dce7e\transformed\jetified-play-services-measurement-sdk-api-17.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\bdcd78434eefd8a81213a729478dce7e\transformed\jetified-play-services-measurement-sdk-api-17.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:26:5-67
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:11:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:12:5-79
MERGED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:25:5-79
MERGED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:25:5-79
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\bdcd78434eefd8a81213a729478dce7e\transformed\jetified-play-services-measurement-sdk-api-17.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\bdcd78434eefd8a81213a729478dce7e\transformed\jetified-play-services-measurement-sdk-api-17.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:22:5-79
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:12:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:13:5-76
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:13:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:14:5-75
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:14:22-73
uses-permission#android.permission.CAMERA
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:16:5-65
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:16:22-62
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:17:5-81
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:17:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:18:5-80
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:18:22-77
uses-permission#android.permission.VIBRATE
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:19:5-66
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:19:22-63
uses-permission#android.permission.FLASHLIGHT
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:20:5-69
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:20:22-66
application
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:25:5-76:19
MERGED from [com.google.android.material:material:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\fb819667f8bcab372a213564cd272f5f\transformed\material-1.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\fb819667f8bcab372a213564cd272f5f\transformed\material-1.0.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\vs\gradle-5.6.4\caches\transforms-3\7bc85073c6f0e7e360d25c1b5cf33d74\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\vs\gradle-5.6.4\caches\transforms-3\7bc85073c6f0e7e360d25c1b5cf33d74\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-17:19
MERGED from [:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-17:19
MERGED from [com.github.Kunzisoft:Android-SwitchDateTimePicker:2.0] C:\vs\gradle-5.6.4\caches\transforms-3\8d8c3637d7a919dae0be65409af292c7\transformed\jetified-Android-SwitchDateTimePicker-2.0\AndroidManifest.xml:11:5-14:19
MERGED from [com.github.Kunzisoft:Android-SwitchDateTimePicker:2.0] C:\vs\gradle-5.6.4\caches\transforms-3\8d8c3637d7a919dae0be65409af292c7\transformed\jetified-Android-SwitchDateTimePicker-2.0\AndroidManifest.xml:11:5-14:19
MERGED from [com.github.prolificinteractive:material-calendarview:1.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\a122cc92abf2571993a259f471130e57\transformed\jetified-material-calendarview-1.5.0\AndroidManifest.xml:11:5-20
MERGED from [com.github.prolificinteractive:material-calendarview:1.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\a122cc92abf2571993a259f471130e57\transformed\jetified-material-calendarview-1.5.0\AndroidManifest.xml:11:5-20
MERGED from [com.google.firebase:firebase-analytics:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\4b3fbc7e49d384c6073427e2516ed6b9\transformed\jetified-firebase-analytics-17.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\4b3fbc7e49d384c6073427e2516ed6b9\transformed\jetified-firebase-analytics-17.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:12:5-20:19
MERGED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:12:5-20:19
MERGED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:24:5-38:19
MERGED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:24:5-38:19
MERGED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:28:5-45:19
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:28:5-45:19
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:26:5-34:19
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:26:5-34:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\e2c2a4e457b826af7f59b494c890816b\transformed\jetified-play-services-measurement-sdk-17.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\e2c2a4e457b826af7f59b494c890816b\transformed\jetified-play-services-measurement-sdk-17.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:28:5-29:19
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:28:5-29:19
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\37af93bade8d2bcbb1527bb6a06377be\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\37af93bade8d2bcbb1527bb6a06377be\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-iid-interop:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\bfc0f35061496c8167916438f98b9e0d\transformed\jetified-firebase-iid-interop-17.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\bfc0f35061496c8167916438f98b9e0d\transformed\jetified-firebase-iid-interop-17.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\011e0e91d27ddd63530d827ec14f33c0\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\011e0e91d27ddd63530d827ec14f33c0\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\18f9df407a0431d546c69bbf640b58e5\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\18f9df407a0431d546c69bbf640b58e5\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:22:5-27:19
MERGED from [com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:22:5-27:19
MERGED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:13:5-21:19
MERGED from [com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:11:5-17:19
MERGED from [com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:11:5-17:19
MERGED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:25:5-37:19
MERGED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:25:5-37:19
MERGED from [com.google.android.gms:play-services-tasks:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ce900338fda5ad5341b0e1f2d4755728\transformed\jetified-play-services-tasks-17.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-tasks:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ce900338fda5ad5341b0e1f2d4755728\transformed\jetified-play-services-tasks-17.0.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\468a4af3c6ac6cabd16f38e0203f1db7\transformed\jetified-firebase-measurement-connector-18.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\468a4af3c6ac6cabd16f38e0203f1db7\transformed\jetified-firebase-measurement-connector-18.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d5b1e7a25eaad23beac6984d0051fad9\transformed\jetified-play-services-ads-identifier-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d5b1e7a25eaad23beac6984d0051fad9\transformed\jetified-play-services-ads-identifier-17.0.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\7f363057f60eaf9c9fc40a70cd970db5\transformed\jetified-play-services-measurement-base-17.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\7f363057f60eaf9c9fc40a70cd970db5\transformed\jetified-play-services-measurement-base-17.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\0dae37a976ed46f7b871d598b1d13ce3\transformed\core-1.3.2\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\0dae37a976ed46f7b871d598b1d13ce3\transformed\core-1.3.2\AndroidManifest.xml:24:5-89
MERGED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\005203ed722122e053e9b8e1ae4ef084\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\005203ed722122e053e9b8e1ae4ef084\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:24:5-25:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\0dae37a976ed46f7b871d598b1d13ce3\transformed\core-1.3.2\AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from [:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-35
	android:label
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:31:9-41
	android:largeHeap
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:29:9-33
	android:icon
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:30:9-49
	android:allowBackup
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:28:9-36
		REJECTED from [:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-35
		REJECTED from [com.github.Kunzisoft:Android-SwitchDateTimePicker:2.0] C:\vs\gradle-5.6.4\caches\transforms-3\8d8c3637d7a919dae0be65409af292c7\transformed\jetified-Android-SwitchDateTimePicker-2.0\AndroidManifest.xml:12:9-35
	android:theme
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:32:9-65
	tools:replace
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:33:9-44
	android:usesCleartextTraffic
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:26:9-44
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:27:9-41
activity#com.longint.lomag.lomagweb.MainActivity
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:35:9-46:20
	android:label
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:37:13-45
	android:windowSoftInputMode
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:38:13-52
	android:exported
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:40:17-40
	android:configChanges
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:36:13-74
	android:theme
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:39:17-53
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:35:19-47
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:42:13-45:29
action#android.intent.action.MAIN
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:43:17-69
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:43:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:44:17-77
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:44:27-74
activity#com.longint.lomag.lomagweb.ScanningActivity
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:48:9-52:71
	android:windowSoftInputMode
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:51:13-60
	android:configChanges
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:50:13-63
	android:theme
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:52:13-69
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:49:13-45
provider#com.longint.lomag.lomagweb.utils.GenericFileProvider
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:54:9-62:20
	android:grantUriPermissions
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:58:13-47
	android:authorities
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:56:13-74
	android:exported
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:57:13-37
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:55:13-54
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:59:13-61:64
	android:resource
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:61:17-51
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:60:17-67
activity#com.longint.lomag.lomagweb.SendLogActivity
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:64:9-73:20
	android:label
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:66:13-45
	android:windowSoftInputMode
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:67:13-52
	android:exported
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:68:13-36
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:65:13-44
intent-filter#action:name:com.longint.lomag.lomagweb.SEND_LOG+category:name:android.intent.category.DEFAULT
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:69:13-72:29
action#com.longint.lomag.lomagweb.SEND_LOG
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:70:17-78
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:70:25-75
category#android.intent.category.DEFAULT
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:71:17-76
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:71:27-73
queries
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:78:5-82:15
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:79:9-81:18
action#android.media.action.IMAGE_CAPTURE
ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:80:13-73
	android:name
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:80:21-70
uses-sdk
INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
MERGED from [androidx.preference:preference:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\6b002b77efe2d635d59dd9c547adbb36\transformed\preference-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\6b002b77efe2d635d59dd9c547adbb36\transformed\preference-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\fb819667f8bcab372a213564cd272f5f\transformed\material-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\fb819667f8bcab372a213564cd272f5f\transformed\material-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\vs\gradle-5.6.4\caches\transforms-3\7bc85073c6f0e7e360d25c1b5cf33d74\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\vs\gradle-5.6.4\caches\transforms-3\7bc85073c6f0e7e360d25c1b5cf33d74\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.Kunzisoft:Android-SwitchDateTimePicker:2.0] C:\vs\gradle-5.6.4\caches\transforms-3\8d8c3637d7a919dae0be65409af292c7\transformed\jetified-Android-SwitchDateTimePicker-2.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.Kunzisoft:Android-SwitchDateTimePicker:2.0] C:\vs\gradle-5.6.4\caches\transforms-3\8d8c3637d7a919dae0be65409af292c7\transformed\jetified-Android-SwitchDateTimePicker-2.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\vs\gradle-5.6.4\caches\transforms-3\0b5730c5530219111c6c8038fb60e71b\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\vs\gradle-5.6.4\caches\transforms-3\0b5730c5530219111c6c8038fb60e71b\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.prolificinteractive:material-calendarview:1.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\a122cc92abf2571993a259f471130e57\transformed\jetified-material-calendarview-1.5.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.prolificinteractive:material-calendarview:1.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\a122cc92abf2571993a259f471130e57\transformed\jetified-material-calendarview-1.5.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ec3bba6edc67982b9dd618e402bc1bba\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ec3bba6edc67982b9dd618e402bc1bba\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ab2d04c3152c165246bd50fb85414d1e\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ab2d04c3152c165246bd50fb85414d1e\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.multidex:multidex:2.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\19e9e6f3f1799c62d257af65fc04d712\transformed\multidex-2.0.0\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\19e9e6f3f1799c62d257af65fc04d712\transformed\multidex-2.0.0\AndroidManifest.xml:20:5-43
MERGED from [com.google.firebase:firebase-analytics:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\4b3fbc7e49d384c6073427e2516ed6b9\transformed\jetified-firebase-analytics-17.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\4b3fbc7e49d384c6073427e2516ed6b9\transformed\jetified-firebase-analytics-17.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\f05ca3f7f3ac99fb02f0a43f29cc7576\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\f05ca3f7f3ac99fb02f0a43f29cc7576\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\5acc49254da2eecabd04d20f979c0388\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\5acc49254da2eecabd04d20f979c0388\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:21:5-23:41
MERGED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:21:5-23:41
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\e2c2a4e457b826af7f59b494c890816b\transformed\jetified-play-services-measurement-sdk-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\e2c2a4e457b826af7f59b494c890816b\transformed\jetified-play-services-measurement-sdk-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\37af93bade8d2bcbb1527bb6a06377be\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\37af93bade8d2bcbb1527bb6a06377be\transformed\jetified-play-services-stats-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\4bff1c84b3c6a5aef74c09b3e29059f0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\4bff1c84b3c6a5aef74c09b3e29059f0\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\48dc1825e3e42dee655a22d56a1235b5\transformed\transition-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\48dc1825e3e42dee655a22d56a1235b5\transformed\transition-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d7b520c337af544206a88043ea327e17\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d7b520c337af544206a88043ea327e17\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\bfc0f35061496c8167916438f98b9e0d\transformed\jetified-firebase-iid-interop-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\bfc0f35061496c8167916438f98b9e0d\transformed\jetified-firebase-iid-interop-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\011e0e91d27ddd63530d827ec14f33c0\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-clearcut:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\011e0e91d27ddd63530d827ec14f33c0\transformed\jetified-play-services-clearcut-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\18f9df407a0431d546c69bbf640b58e5\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-phenotype:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\18f9df407a0431d546c69bbf640b58e5\transformed\jetified-play-services-phenotype-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:6:5-8:41
MERGED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-installations-interop:16.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ce9e8986e59fd54553e07493d9a2fd93\transformed\jetified-firebase-installations-interop-16.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-installations-interop:16.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ce9e8986e59fd54553e07493d9a2fd93\transformed\jetified-firebase-installations-interop-16.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.gms:play-services-tasks:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ce900338fda5ad5341b0e1f2d4755728\transformed\jetified-play-services-tasks-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ce900338fda5ad5341b0e1f2d4755728\transformed\jetified-play-services-tasks-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\468a4af3c6ac6cabd16f38e0203f1db7\transformed\jetified-firebase-measurement-connector-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:18.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\468a4af3c6ac6cabd16f38e0203f1db7\transformed\jetified-firebase-measurement-connector-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d5b1e7a25eaad23beac6984d0051fad9\transformed\jetified-play-services-ads-identifier-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d5b1e7a25eaad23beac6984d0051fad9\transformed\jetified-play-services-ads-identifier-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\bdcd78434eefd8a81213a729478dce7e\transformed\jetified-play-services-measurement-sdk-api-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\bdcd78434eefd8a81213a729478dce7e\transformed\jetified-play-services-measurement-sdk-api-17.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\7f363057f60eaf9c9fc40a70cd970db5\transformed\jetified-play-services-measurement-base-17.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\7f363057f60eaf9c9fc40a70cd970db5\transformed\jetified-play-services-measurement-base-17.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\9a67fc43e4892495db6a69d59ca55315\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\9a67fc43e4892495db6a69d59ca55315\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\c34e35acf4c8880e27899cc1430ce44a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\c34e35acf4c8880e27899cc1430ce44a\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\1714e6670f8230ea9a01fb5649cf2a69\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\1714e6670f8230ea9a01fb5649cf2a69\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\72aefe36bf2238cc0bb2b823f58f4a35\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\72aefe36bf2238cc0bb2b823f58f4a35\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\0d5c89cb173b24a90424b6ea6099597c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\0d5c89cb173b24a90424b6ea6099597c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\449eba516b9a2ec0573a5f6ef09f9f2b\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\449eba516b9a2ec0573a5f6ef09f9f2b\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d0ff6ec68c203861eb4eabe2859e8162\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d0ff6ec68c203861eb4eabe2859e8162\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\1efe95b7d94cae64b87f2cb1b9cf468e\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\1efe95b7d94cae64b87f2cb1b9cf468e\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\dff4fa6ad7f91ac2faf8765f429d526c\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\dff4fa6ad7f91ac2faf8765f429d526c\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\vs\gradle-5.6.4\caches\transforms-3\67fb4ad2733e1d0fc0f4bc484fca152c\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\vs\gradle-5.6.4\caches\transforms-3\67fb4ad2733e1d0fc0f4bc484fca152c\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\b4a6aaf082da9b1cbfea42e8548f0e3f\transformed\jetified-activity-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\b4a6aaf082da9b1cbfea42e8548f0e3f\transformed\jetified-activity-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\55031aa06c25f8cc72da74c1dbe4285b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\55031aa06c25f8cc72da74c1dbe4285b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\920ab1b4d2e34f305e98b5b500f149e4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\920ab1b4d2e34f305e98b5b500f149e4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\0dae37a976ed46f7b871d598b1d13ce3\transformed\core-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\0dae37a976ed46f7b871d598b1d13ce3\transformed\core-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:16.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\f1964c3bf13626cc6eeb7d737e73d38f\transformed\jetified-firebase-components-16.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:16.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\f1964c3bf13626cc6eeb7d737e73d38f\transformed\jetified-firebase-components-16.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:16.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\b86ae37eaeaf6f6bc6564c89cbb7b712\transformed\jetified-firebase-encoders-json-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:16.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\b86ae37eaeaf6f6bc6564c89cbb7b712\transformed\jetified-firebase-encoders-json-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\6d9a378dd87f9a1873292bc036387f09\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\6d9a378dd87f9a1873292bc036387f09\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\e67690560cf8471bb54b6e9f02910e5e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\e67690560cf8471bb54b6e9f02910e5e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\c9a14066f66d5cc9afca129a51f6a3f0\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\c9a14066f66d5cc9afca129a51f6a3f0\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\7dfa5acbff5291c2e54a16ad3a753b3a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\7dfa5acbff5291c2e54a16ad3a753b3a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\95476d54cc1a6555a8ab0b460dca0d58\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\95476d54cc1a6555a8ab0b460dca0d58\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\005203ed722122e053e9b8e1ae4ef084\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\005203ed722122e053e9b8e1ae4ef084\transformed\versionedparcelable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\2e9433368b23004bc3e0c1ecacbc6e74\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\2e9433368b23004bc3e0c1ecacbc6e74\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\a2ed1a288c8cd6f2ef584c97cf68a350\transformed\lifecycle-livedata-core-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\a2ed1a288c8cd6f2ef584c97cf68a350\transformed\lifecycle-livedata-core-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d4b9ad3bf9a35e8b37b46241d826f1c4\transformed\core-runtime-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\d4b9ad3bf9a35e8b37b46241d826f1c4\transformed\core-runtime-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\e9ceaa3a6d9d57dedc51e43f776427b6\transformed\lifecycle-runtime-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\e9ceaa3a6d9d57dedc51e43f776427b6\transformed\lifecycle-runtime-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\adb944c16195823e129769fab694bd18\transformed\lifecycle-viewmodel-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.1.0] C:\vs\gradle-5.6.4\caches\transforms-3\adb944c16195823e129769fab694bd18\transformed\lifecycle-viewmodel-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\e66c1242e4f8915f12bb40c95a0b142a\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\e66c1242e4f8915f12bb40c95a0b142a\transformed\jetified-savedstate-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-api:2.2.0] C:\vs\gradle-5.6.4\caches\transforms-3\f05b285998803e22de438107db46e04c\transformed\jetified-transport-api-2.2.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.0] C:\vs\gradle-5.6.4\caches\transforms-3\f05b285998803e22de438107db46e04c\transformed\jetified-transport-api-2.2.0\AndroidManifest.xml:18:5-20:41
INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
		INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
		ADDED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
		INJECTED from C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
activity#jim.h.common.android.lib.zxing.CaptureActivity
ADDED from [:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-16:63
	android:windowSoftInputMode
		ADDED from [:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-60
	android:configChanges
		ADDED from [:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-63
	android:name
		ADDED from [:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-74
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:13:9-19:19
MERGED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:31:9-37:19
MERGED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:31:9-37:19
MERGED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:27:9-33:19
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:27:9-33:19
MERGED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:14:9-20:19
MERGED from [com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:12:9-16:19
MERGED from [com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:12:9-16:19
MERGED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:32:9-36:35
MERGED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:32:9-36:35
	android:exported
		ADDED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:15:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:36:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:34:13-43
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:14:13-84
meta-data#com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar
ADDED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:16:13-18:85
	android:value
		ADDED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:18:17-82
	android:name
		ADDED from [com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:17:17-115
provider#com.google.firebase.perf.provider.FirebasePerfProvider
ADDED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:25:9-29:39
	android:authorities
		ADDED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:27:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:28:13-37
	android:initOrder
		ADDED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:29:13-36
	android:name
		ADDED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:26:13-82
meta-data#com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar
ADDED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:34:13-36:85
	android:value
		ADDED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:36:17-82
	android:name
		ADDED from [com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:35:17-109
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\bdcd78434eefd8a81213a729478dce7e\transformed\jetified-play-services-measurement-sdk-api-17.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\bdcd78434eefd8a81213a729478dce7e\transformed\jetified-play-services-measurement-sdk-api-17.5.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:25:22-65
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\d62e28d87f21a3fa31ec237e2435ac87\transformed\jetified-play-services-measurement-impl-17.5.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:40:13-87
meta-data#com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar
ADDED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:33:17-117
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:26:5-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:26:22-79
meta-data#com.google.firebase.components:com.google.firebase.iid.Registrar
ADDED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:33:17-96
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:37:9-44:20
	android:exported
		ADDED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:39:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:40:13-73
	android:name
		ADDED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:38:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:41:13-43:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:42:17-81
	android:name
		ADDED from [com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:42:25-78
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:31:17-139
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:23:9-26:75
	android:exported
		ADDED from [com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:26:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:24:13-79
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:18:17-127
meta-data#com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar
ADDED from [com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:13:13-15:85
	android:value
		ADDED from [com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:15:17-82
	android:name
		ADDED from [com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:14:17-109
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:26:9-30:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:28:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:29:13-37
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:30:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:27:13-77
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:23:9-25:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:25:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:24:13-58
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:33:13-132
