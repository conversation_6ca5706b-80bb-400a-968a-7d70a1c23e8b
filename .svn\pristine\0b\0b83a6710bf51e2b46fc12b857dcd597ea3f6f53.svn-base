package com.longint.lomag.lomagweb.fragments;

import android.app.Activity;
import android.app.AlertDialog;
import androidx.fragment.app.Fragment;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;

import androidx.appcompat.app.AppCompatActivity;

import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ListView;
import com.longint.lomag.lomagweb.LomagApplication;
import com.longint.lomag.lomagweb.MainActivity;
import com.longint.lomag.lomagweb.R;
import com.longint.lomag.lomagweb.adapters.WarehouseItemsForDocumentAdapter;
import com.longint.lomag.lomagweb.data.SelectedWarehouseData;
import com.longint.lomag.lomagweb.db.procedures.Procedure;
import com.longint.lomag.lomagweb.db.procedures.delete.RemoveStockItemFromDocumentProcedure;
import com.longint.lomag.lomagweb.db.resultAsyncTasks.ProcedureExecutionAsyncTask;
import com.longint.lomag.lomagweb.db.procedures.get.GetSerialsForItemInDocumentProcedure;
import com.longint.lomag.lomagweb.db.toobjects.Document;
import com.longint.lomag.lomagweb.db.toobjects.DocumentType;
import com.longint.lomag.lomagweb.db.toobjects.SerialNumber;
import com.longint.lomag.lomagweb.db.toobjects.StockItem;
import com.longint.lomag.lomagweb.utils.ListWaresForDocumentDataLoader;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class WaresForDocumentPreviewFragment extends Fragment
                                            implements WarehouseItemsForDocumentAdapter.WarehouseItemsForDocumentListListener,
                                                ProcedureExecutionAsyncTask.ProcedureExecutionListener {

    private ListView listViewStockItems;
    private EditText searchDocEditText;
    private WarehouseItemsForDocumentAdapter adapter; //TODO change
    private List<StockItem> items;
    private List<Integer> mBackground;
    private Document document;
    private ListWaresForDocumentDataLoader dataLoader;

    public void setDocument(Document document) {
        this.document = document;
    }

    public interface WaresForDocumentPreviewFragmentListener {
        void onEditOfferOrderClicked(StockItem items, Document item);
    }

    private WaresForDocumentPreviewFragmentListener mListener;
    private MainActivity mainActivity;

    private int get_idAlgorithm=0,documentTypeId,idRuchuMagazynowego;

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);

        try {
            mainActivity = (MainActivity) context;
            mListener = (WaresForDocumentPreviewFragmentListener) context;
        } catch (ClassCastException e) {
            throw new ClassCastException(context.toString() + " must implement WaresForDocumentPreviewFragmentListener");
        }
    }

    @Override
    public void onAttach(Activity context) {
        super.onAttach(context);

        try {
            mainActivity = (MainActivity) context;
            mListener = (WaresForDocumentPreviewFragmentListener) context;
        } catch (ClassCastException e) {
            throw new ClassCastException(context.toString() + " must implement WaresForDocumentPreviewFragmentListener");
        }
    }

    @Override
    public void onDeleteButtonClicked(final StockItem item) {
        AlertDialog dialog = new AlertDialog.Builder(getActivity())
                .setMessage(String.format(getString(R.string.remove_item), item.getName()))
                .setPositiveButton(getString(R.string.yes), new DialogInterface.OnClickListener() {
                    public void onClick(final DialogInterface dialog, int which) {
                        RemoveStockItemFromDocumentProcedure procedure = new RemoveStockItemFromDocumentProcedure(idRuchuMagazynowego, item,documentTypeId);
                        ProcedureExecutionAsyncTask procedureAsyncTask = new ProcedureExecutionAsyncTask(
                                procedure,
                                WaresForDocumentPreviewFragment.this
                        );
                        procedureAsyncTask.execute();
                        dialog.dismiss();
                    }
                }).setNegativeButton(getString(R.string.no), new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                }).setIcon(android.R.drawable.ic_dialog_alert).show();
    }

    @Override
    public void onItemClicked(StockItem item) {
        // Show serial numbers preview if they exist
        showSerialNumbersPreview(item);
    }

    @Override
    public void onEditButtonClicked(StockItem item) {
        if(documentTypeId==DocumentType.RENTAL_ID || documentTypeId==DocumentType.RETURN_FROM_RENTAL_ID)
        {
            //listener.onAddButtonRentalClicked(item);
        }
        else if(documentTypeId==DocumentType.SUPPLIER_ORDER_ZD_ID || documentTypeId==DocumentType.OFFER_OF_ID || documentTypeId==DocumentType.CUSTOMER_ORDER_ZO_ID)
        {
            SelectedWarehouseData.getInstance().setItemAddedToInv(item);
            SelectedWarehouseData.getInstance().setAddingDocumentElementFlag(false);
            SelectedWarehouseData.getInstance().setCurrentDocType(this.document.getDocumentType());
            SelectedWarehouseData.getInstance().setCurrentDocument(this.document);

            mListener.onEditOfferOrderClicked(item, this.document);
        }
        else if(documentTypeId==DocumentType.INVOICE_OF_ID || documentTypeId==DocumentType.CORRECTION_INVOICE_OF_ID || documentTypeId==DocumentType.PROFORMA_OF_ID)
        {
            //listener.onAddButtonInvoiceClicked(item);
        }
        else
        {
            //listener.addWareToDocument(item);
        }
    }

    @Override
    public void onProcedureExecuted(Object result, Procedure procedure) {
        if (procedure instanceof RemoveStockItemFromDocumentProcedure) {
            updateItems();
        }
    }

    @Override
    public void onConnectionFailed(
            Procedure procedure,
            ProcedureExecutionAsyncTask.ProcedureExecutionListener listener,
            Exception e
    ) {
        Activity activity = getActivity();

        if (activity == null) {
            return;
        }

        if (procedure instanceof RemoveStockItemFromDocumentProcedure) {
            new AlertDialog.Builder(activity)
                .setMessage(getString(R.string.some_items_were_not_removed))
                .setPositiveButton(getString(R.string.ok), new DialogInterface.OnClickListener() {
                    public void onClick(final DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                })
                .setIcon(android.R.drawable.ic_dialog_alert).show()
                .show();
        }
        else {
            ((MainActivity)activity).onConnectionFailed(procedure, listener, e);
        }
    }

    private void findViews(View view)
    {

        listViewStockItems = view.findViewById(R.id.listViewWarehouseItems);
        searchDocEditText = view.findViewById(R.id.editTextSearchDoc);
     //   editTextSearch = (EditText) view.findViewById(R.id.editTextSearch);
     //   frameLayoutAddItem = (FrameLayout) view.findViewById(R.id.frameLayoutAddItem);
     //   frameLayoutRemoveItem = (FrameLayout) view.findViewById(R.id.frameLayoutRemoveItems);
     //   imageViewScan = (ImageView) view.findViewById(R.id.imageViewScanIcon);
     //   imageViewSearchIcon = (ImageView)  view.findViewById(R.id.imageViewSearchIcon);
    }

    private void setActionBar()
    {
        ((AppCompatActivity) getActivity()).getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        ((AppCompatActivity) getActivity()).getSupportActionBar().setDisplayShowHomeEnabled(true);
        ((AppCompatActivity) getActivity()).getSupportActionBar().setTitle(R.string.wares_of_document);
        ((MainActivity)getActivity()).setMenuVisibility(false);
        ((MainActivity)getActivity()).setRefreashVisibility(false);
        getActivity().invalidateOptionsMenu();
    }

    public void updateItems(){
        if (dataLoader!=null)
            dataLoader.resetData();
    }

    @Override
    public void onResume() {
        setActionBar();
        super.onResume();
    }

    public void onItemEdited(StockItem newItem)
    {
        adapter.setItem(SelectedWarehouseData.getInstance().getEditedItemPos(),newItem);
        SelectedWarehouseData.getInstance().setEditedElement(null);
    }

    private void setList()
    {
        View footerView = ((LayoutInflater) getActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.footer_list_loading, null, false);
        items = new LinkedList<StockItem>();
        listViewStockItems.addFooterView(footerView);
        adapter = new WarehouseItemsForDocumentAdapter(getActivity(), items, footerView, this, documentTypeId);
        listViewStockItems.setAdapter(adapter);
        dataLoader = new ListWaresForDocumentDataLoader(get_idAlgorithm,adapter,200,50,200,footerView,listViewStockItems, this, getActivity());
        dataLoader.setDocId(idRuchuMagazynowego);
        dataLoader.setocumentTypeId(documentTypeId);

        if(documentTypeId== DocumentType.INVOICE_OF_ID || documentTypeId== DocumentType.CORRECTION_INVOICE_OF_ID || documentTypeId== DocumentType.PROFORMA_OF_ID)
        {
            dataLoader.setInvoice_Algorithm(get_idAlgorithm);
        }

        listViewStockItems.setOnScrollListener(dataLoader);
    }


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {

        View view = inflater.inflate(R.layout.fragment_actual_wares_by_document, null);
        findViews(view);

        idRuchuMagazynowego = getArguments().getInt("IDRuchuMagazynowego");
        documentTypeId = getArguments().getInt("documentTypeId");

        if(documentTypeId== DocumentType.INVOICE_OF_ID || documentTypeId== DocumentType.CORRECTION_INVOICE_OF_ID || documentTypeId== DocumentType.PROFORMA_OF_ID)
        {
            get_idAlgorithm= getArguments().getInt("idAlgorithm");
        }

        Log.v("testy2223","id "+idRuchuMagazynowego);
        Log.v("testy2223","documentTypeId "+documentTypeId);
        Log.v("testy2223","get_idAlgorithm "+get_idAlgorithm);

        setList();
        setOtherViews();

        return view;
    }

    private void setOtherViews() {
        searchDocEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            boolean isEnter = false;

            @Override
            public void afterTextChanged(Editable s) {
                String txt = s.toString();
                if (txt.endsWith("\n")) {
                    isEnter = true;
                    searchDocEditText.setText(txt.trim());
                } else {
                    dataLoader.setSearchText(s.toString(),isEnter);
                    isEnter = false;
                }
            }
        });
        searchDocEditText.requestFocus();

    }

    /**
     * Show serial numbers preview for selected item if they exist
     */
    private void showSerialNumbersPreview(StockItem item) {
        try {
            // Start procedure to get serial numbers for this item in the document
            GetSerialsForItemInDocumentProcedure procedure = new GetSerialsForItemInDocumentProcedure(
                    item.getIdWareForDocument(),
                    idRuchuMagazynowego
            );

            ProcedureExecutionAsyncTask procedureAsyncTask = new ProcedureExecutionAsyncTask(procedure, new ProcedureExecutionAsyncTask.ProcedureExecutionListener() {
                @Override
                public void onProcedureExecuted(Object result, Procedure procedure) {
                    try {
                        GetSerialsForItemInDocumentProcedure serialsProcedure = (GetSerialsForItemInDocumentProcedure) procedure;
                        ArrayList<SerialNumber> serialNumbers = serialsProcedure.getSerialNumbers();

                        if (serialNumbers != null && !serialNumbers.isEmpty()) {
                            // Show serial numbers in a new fragment/dialog
                            showSerialNumbersDialog(item, serialNumbers);
                        }
                        // If no serial numbers, do nothing (window doesn't open)

                    } catch (Exception e) {
                        Log.e(LomagApplication.APP_TAG, "Error processing serial numbers: " + e);
                    }
                }

                @Override
                public void onConnectionFailed(Procedure procedure, ProcedureExecutionAsyncTask.ProcedureExecutionListener listener, Exception e) {
                    Log.e(LomagApplication.APP_TAG, "Failed to get serial numbers: " + e);
                    // Do nothing on error
                }
            });
            procedureAsyncTask.execute();

        } catch (Exception e) {
            Log.e(LomagApplication.APP_TAG, "Error starting serial numbers preview: " + e);
        }
    }

    /**
     * Show dialog with serial numbers for the item
     */
    private void showSerialNumbersDialog(StockItem item, ArrayList<SerialNumber> serialNumbers) {
        Activity activity = getActivity();
        if (activity != null) {
            StringBuilder serialsText = new StringBuilder();
            serialsText.append(getString(R.string.item_name)).append(item.getName()).append("\n\n");
            serialsText.append(getString(R.string.serial_numbers)).append(":\n");

            for (int i = 0; i < serialNumbers.size(); i++) {
                SerialNumber serial = serialNumbers.get(i);
                serialsText.append((i + 1)).append(". ").append(serial.getSerialNr()).append("\n");
            }

            new AlertDialog.Builder(activity)
                    .setTitle(getString(R.string.serial_numbers_preview))
                    .setMessage(serialsText.toString())
                    .setPositiveButton(android.R.string.ok, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            dialog.dismiss();
                        }
                    })
                    .show();
        }
    }
}
