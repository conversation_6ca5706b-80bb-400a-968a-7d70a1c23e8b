package com.longint.lomag.lomagweb.utils;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.util.Log;

import com.longint.lomag.lomagweb.LomagApplication;
import com.longint.lomag.lomagweb.R;
import com.longint.lomag.lomagweb.data.SelectedWarehouseData;
import com.longint.lomag.lomagweb.db.toobjects.DedicatedColumn;
import com.longint.lomag.lomagweb.db.toobjects.DocumentElement;
import com.longint.lomag.lomagweb.db.toobjects.SerialNumber;
import com.longint.lomag.lomagweb.db.toobjects.StockItem;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Utility class for validating dedicated columns
 */
public class DedicatedColumnsValidator {

    /**
     * Validate dedicated columns for a single serial number
     * @param serial Serial number to validate
     * @param dedicatedColumns List of dedicated columns configuration
     * @param activity Activity context for showing error dialogs
     * @return true if validation passes, false otherwise
     */
    public static boolean validateSingleSerialDedicatedColumns(SerialNumber serial, ArrayList<DedicatedColumn> dedicatedColumns, Activity activity) {
        for (DedicatedColumn dc : dedicatedColumns) {
            if ("1".equalsIgnoreCase(dc.get_mandatory())) {
                // Check if this mandatory column has a value
                HashMap<String, String> dcData = serial.getDedicatedColumnsData();
                String value = dcData != null ? dcData.get(dc.get_column()) : null;

                if (value == null || value.trim().isEmpty()) {
                    // Show error for missing mandatory field
                    showMandatoryDedicatedColumnError(dc.get_name(), serial.getSerialNr(), activity);
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Check if any dedicated column is mandatory
     * @param dedicatedColumns List of dedicated columns configuration
     * @return true if at least one column is mandatory, false otherwise
     */
    public static boolean hasMandatoryColumns(ArrayList<DedicatedColumn> dedicatedColumns) {
        if (dedicatedColumns == null) {
            return false;
        }
        
        for (DedicatedColumn dc : dedicatedColumns) {
            if ("1".equalsIgnoreCase(dc.get_mandatory())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Show error dialog for mandatory dedicated column validation
     * @param columnName Name of the mandatory column
     * @param serialNumber Serial number that failed validation
     * @param activity Activity context for showing the dialog
     */
    public static void showMandatoryDedicatedColumnError(String columnName, String serialNumber, Activity activity) {
        if (activity != null) {
            String itemName = getCurrentItemName();
            
            String message = activity.getString(R.string.dedicated_column_mandatory_error, columnName, serialNumber, itemName);
            new AlertDialog.Builder(activity)
                    .setTitle(R.string.error)
                    .setMessage(message)
                    .setPositiveButton(android.R.string.ok, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            dialog.dismiss();
                        }
                    })
                    .setCancelable(false)
                    .show();
        }
    }

    /**
     * Get current item name from SelectedWarehouseData
     * @return Current item name or "Unknown Item" if not available
     */
    public static String getCurrentItemName() {
        try {
            DocumentElement currentDocElement = SelectedWarehouseData.getInstance().getCurrentDocElement();
            if (currentDocElement != null && currentDocElement.getItem() != null) {
                return currentDocElement.getItem().getName();
            }

            StockItem currentItem = SelectedWarehouseData.getInstance().getCurrentItem();
            if (currentItem != null) {
                return currentItem.getName();
            }

            return "Unknown Item";
        } catch (Exception e) {
            Log.e(LomagApplication.APP_TAG, "Error getting current item name: " + e);
            return "Unknown Item";
        }
    }

    /**
     * Format field name with mandatory indicator
     * @param fieldName Original field name
     * @param isMandatory Whether the field is mandatory
     * @return Formatted field name with * if mandatory
     */
    public static String formatFieldName(String fieldName, boolean isMandatory) {
        if (isMandatory) {
            return fieldName + " *";
        }
        return fieldName;
    }
}
