package com.longint.lomag.lomagweb.fragments;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.AsyncTask;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.text.InputType;
import android.widget.AutoCompleteTextView;
import android.widget.CheckBox;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import androidx.fragment.app.Fragment;
import com.longint.lomag.lomagweb.LomagApplication;
import com.longint.lomag.lomagweb.R;
import com.longint.lomag.lomagweb.adapters.SpinnerArrayAdapter;
import com.longint.lomag.lomagweb.data.SelectedWarehouseData;
import com.longint.lomag.lomagweb.db.resultAsyncTasks.ProcedureExecutionAsyncTask;
import com.longint.lomag.lomagweb.db.procedures.Procedure;
import com.longint.lomag.lomagweb.db.procedures.get.GetDedicatedColumnProcedure;
import com.longint.lomag.lomagweb.db.procedures.get.GetDedicatedComboDictProcedure;
import com.longint.lomag.lomagweb.db.toobjects.DedicatedColumn;
import com.longint.lomag.lomagweb.db.toobjects.DedicatedComboDict;
import com.longint.lomag.lomagweb.db.toobjects.DocumentElement;
import com.longint.lomag.lomagweb.db.toobjects.SerialNumber;
import com.longint.lomag.lomagweb.db.toobjects.StockItem;
import com.longint.lomag.lomagweb.utils.DedicatedColumnsValidator;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;

public class SerialNumberDedicatedColumnsFragment extends Fragment implements ProcedureExecutionAsyncTask.ProcedureExecutionListener {

    private SerialNumber serialNumber;
    private LinearLayout ll_dedicated_column_main;
    private RelativeLayout relativeLayoutSaveButton;
    private RelativeLayout relativeLayoutCancelButton;
    private TextView textViewSaveButton;
    private TextView textViewCancelButton;

    // Dedicated columns variables
    private ArrayList<DedicatedColumn> dedicatedColumn_list = new ArrayList<>();
    private final ArrayList<String> dedicated_Key_list = new ArrayList<>();
    private final ArrayList<Object> stockItems_values_list = new ArrayList<>();
    private HashMap<String, String> existingColumnValues = new HashMap<>();

    private boolean dedicatedColumnsLoaded = false;
    private boolean comboDictLoaded = false;
    private ArrayList<DedicatedComboDict> dedicatedComboDict_List = new ArrayList<>();
    private int dedicated_column_size = 1;
    private LinearLayout[] parent_bp = new LinearLayout[dedicated_column_size];
    private LinearLayout[] ll_dedicated_column = new LinearLayout[dedicated_column_size];
    private LinearLayout[] ll_spinner_values = new LinearLayout[dedicated_column_size];
    private TextView[] txt_key_field = new TextView[dedicated_column_size];
    private EditText[] edt_value = new EditText[dedicated_column_size];
    private EditText[] edt_spnr_value = new EditText[dedicated_column_size];
    private ImageView[] img_dialog_item = new ImageView[dedicated_column_size];
    private Spinner[] spnr_value = new Spinner[dedicated_column_size];
    private TextView[] txt_dtp_value = new TextView[dedicated_column_size];
    private CheckBox[] chk_value = new CheckBox[dedicated_column_size];
    private AutoCompleteTextView[] autocomplete_txt_value = new AutoCompleteTextView[dedicated_column_size];
    private ImageView[] img_Scan_Code = new ImageView[dedicated_column_size];

    private LayoutInflater inflater;

    public interface SerialNumberDedicatedColumnsListener {
        void onSerialNumberDedicatedColumnsFragmentCreated(SerialNumberDedicatedColumnsFragment fragment);
        void onSaveSerialNumberDedicatedColumns(SerialNumber serialNumber);
        void onCancelSerialNumberDedicatedColumns();
    }

    private SerialNumberDedicatedColumnsListener mListener;

    public static SerialNumberDedicatedColumnsFragment newInstance(SerialNumber serialNumber) {
        SerialNumberDedicatedColumnsFragment fragment = new SerialNumberDedicatedColumnsFragment();
        Bundle args = new Bundle();
        args.putSerializable("serialNumber", serialNumber);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            serialNumber = (SerialNumber) getArguments().getSerializable("serialNumber");
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        this.inflater = inflater;
        View view = inflater.inflate(R.layout.fragment_serial_number_dedicated_columns, container, false);

        ll_dedicated_column_main = view.findViewById(R.id.ll_dedicated_column_main);
        relativeLayoutSaveButton = view.findViewById(R.id.relativeLayoutButtonSave);
        relativeLayoutCancelButton = view.findViewById(R.id.relativeLayoutButtonCancel);
        textViewSaveButton = view.findViewById(R.id.textViewSave);
        textViewCancelButton = view.findViewById(R.id.textViewCancel);

        setupButtonListeners();
        startGetDedicatedComboDictProcedure();
        startGetDedicatedColumnProcedure();

        mListener.onSerialNumberDedicatedColumnsFragmentCreated(this);

        return view;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof SerialNumberDedicatedColumnsListener) {
            mListener = (SerialNumberDedicatedColumnsListener) context;
        } else {
            throw new RuntimeException(context
                    + " must implement SerialNumberDedicatedColumnsListener");
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        mListener = null;
    }

    private void setupButtonListeners() {
        relativeLayoutSaveButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveSerialNumberDedicatedColumns();
            }
        });

        relativeLayoutCancelButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mListener.onCancelSerialNumberDedicatedColumns();
            }
        });
    }

    private void startGetDedicatedColumnProcedure() {
        GetDedicatedColumnProcedure procedure = new GetDedicatedColumnProcedure("SerialNumbersDC", "");
        ProcedureExecutionAsyncTask procedureAsyncTask = new ProcedureExecutionAsyncTask(procedure, this);
        procedureAsyncTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
    }

    private void startGetDedicatedComboDictProcedure() {
        GetDedicatedComboDictProcedure procedure = new GetDedicatedComboDictProcedure();
        ProcedureExecutionAsyncTask procedureAsyncTask = new ProcedureExecutionAsyncTask(procedure, this);
        procedureAsyncTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
    }

    @Override
    public void onProcedureExecuted(Object result, Procedure procedure) {
        if (procedure instanceof GetDedicatedColumnProcedure) {
            try {
                onGetDedicatedColumnProcedureExecuted((GetDedicatedColumnProcedure) procedure);
            } catch (SQLException e) {
                Log.e(LomagApplication.APP_TAG, "Error executing GetDedicatedColumnProcedure", e);
            }
        } else if (procedure instanceof GetDedicatedComboDictProcedure) {
            try {
                onGetDedicatedComboDictProcedureExecuted((GetDedicatedComboDictProcedure) procedure);
            } catch (SQLException e) {
                Log.e(LomagApplication.APP_TAG, "Error executing GetDedicatedComboDictProcedure", e);
            }
        }
    }

    @Override
    public void onConnectionFailed(Procedure procedure, ProcedureExecutionAsyncTask.ProcedureExecutionListener listener, Exception e) {
        Log.e(LomagApplication.APP_TAG, "Failed to execute procedure", e);
    }

    private void onGetDedicatedColumnProcedureExecuted(GetDedicatedColumnProcedure procedure) throws SQLException {
        if (getActivity() == null)
            return;

        dedicatedColumn_list = procedure.getDedicatedColumn_List();
        for (int i = 0; i < dedicatedColumn_list.size(); i++) {
            DedicatedColumn dedicatedColumn = dedicatedColumn_list.get(i);
            dedicated_Key_list.add(dedicatedColumn.get_column());
        }

        // Load existing values from SerialNumber object
        loadExistingValuesFromSerialNumber();

        dedicatedColumnsLoaded = true;
        checkAndDisplayColumns();
    }

    private void loadExistingValuesFromSerialNumber() {
        existingColumnValues = new HashMap<>();

        if (serialNumber != null && serialNumber.getDedicatedColumnsData() != null) {
            // Use actual column names from database configuration
            HashMap<String, String> dcData = serialNumber.getDedicatedColumnsData();

            for (int i = 0; i < dedicatedColumn_list.size(); i++) {
                DedicatedColumn dedicatedColumn = dedicatedColumn_list.get(i);
                String columnName = dedicatedColumn.get_column(); // Use actual column name
                String value = dcData.get(columnName);

                if (value != null) {
                    existingColumnValues.put(columnName, value);
                }
            }
        }
    }

    private void onGetDedicatedComboDictProcedureExecuted(GetDedicatedComboDictProcedure procedure) throws SQLException {
        if (getActivity() == null)
            return;

        dedicatedComboDict_List = procedure.getDedicatedComboDict_List();
        Log.e(LomagApplication.APP_TAG, "onGetDedicatedComboDictProcedureExecuted - dedicatedComboDict_List " + dedicatedComboDict_List.size());

        comboDictLoaded = true;
        checkAndDisplayColumns();
    }

    private void checkAndDisplayColumns() {
        if (dedicatedColumnsLoaded && comboDictLoaded) {
            Log.e(LomagApplication.APP_TAG, "Both procedures completed, displaying columns");
            display_dedicated_columns();
        } else {
            Log.e(LomagApplication.APP_TAG, "Waiting for procedures: dedicatedColumnsLoaded=" + dedicatedColumnsLoaded + ", comboDictLoaded=" + comboDictLoaded);
        }
    }

    private ArrayList<String> get_dedicated_ComboDict(String str_get_column_name) {
        ArrayList<String> str_array_list = new ArrayList<String>();

        Log.e(LomagApplication.APP_TAG, "get_dedicated_ComboDict dedicatedComboDict_List " + (dedicatedComboDict_List != null ? dedicatedComboDict_List.size() : "null"));

        if (dedicatedComboDict_List != null) {
            if (dedicatedComboDict_List.size() > 0) {
                for (int i = 0; i < dedicatedComboDict_List.size(); i++) {
                    DedicatedComboDict dedicatedComboDict = dedicatedComboDict_List.get(i);

                    Log.e(LomagApplication.APP_TAG, "get_dedicated_ComboDict str_get_column_name " + str_get_column_name);
                    if (str_get_column_name.equalsIgnoreCase(dedicatedComboDict.get_column_name())) {
                        String str_get_value_name = dedicatedComboDict.get_value_name();
                        Log.e(LomagApplication.APP_TAG, "get_dedicated_ComboDict str_get_value_name " + str_get_value_name);

                        str_array_list.add(str_get_value_name);
                    }
                }
            }
        }

        return str_array_list;
    }



    private void display_dedicated_columns() {
        if (dedicatedColumn_list != null) {
                dedicated_column_size = dedicatedColumn_list.size();

                parent_bp = new LinearLayout[dedicated_column_size];
                ll_dedicated_column = new LinearLayout[dedicated_column_size];
                txt_key_field = new TextView[dedicated_column_size];
                edt_value = new EditText[dedicated_column_size];
                edt_spnr_value = new EditText[dedicated_column_size];
                img_dialog_item = new ImageView[dedicated_column_size];
                ll_spinner_values = new LinearLayout[dedicated_column_size];
                spnr_value = new Spinner[dedicated_column_size];
                txt_dtp_value = new TextView[dedicated_column_size];
                chk_value = new CheckBox[dedicated_column_size];
                autocomplete_txt_value = new AutoCompleteTextView[dedicated_column_size];
                img_Scan_Code = new ImageView[dedicated_column_size];

                // First pass - get data for each field type (like in NewDocumentFragment)
                ArrayList<ArrayList<String>> str_array_lists = new ArrayList<>();

                for (int i = 0; i < dedicatedColumn_list.size(); i++) {
                    DedicatedColumn dedicatedColumn = dedicatedColumn_list.get(i);
                    String str_get_name = dedicatedColumn.get_name();
                    String str_get_type = dedicatedColumn.get_type();
                    String str_get_precision = dedicatedColumn.get_precision();
                    String str_get_mandatory = dedicatedColumn.get_mandatory();

                    ArrayList<String> str_array_list = new ArrayList<>();

                    if (!TextUtils.isEmpty(str_get_type)) {
                        if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_STRING)) {
                            str_array_list.add(DedicatedColumn.DedicatedColumn_DATA_TYPE_STRING);
                        } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_COMBODEF)) {
                            str_array_list = get_dedicated_ComboDict(str_get_name);
                        } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_DECIMAL)) {
                            str_array_list.add(DedicatedColumn.DedicatedColumn_DATA_TYPE_DECIMAL);
                        } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_BOOL)) {
                            str_array_list.add(DedicatedColumn.DedicatedColumn_DATA_TYPE_BOOL);
                        } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_DATE)) {
                            str_array_list.add(DedicatedColumn.DedicatedColumn_DATA_TYPE_DATE);
                        }
                    }

                    str_array_lists.add(str_array_list);
                }

                // Second pass - create UI elements
                for (int i = 0; i < dedicatedColumn_list.size(); i++) {
                    DedicatedColumn dedicatedColumn = dedicatedColumn_list.get(i);
                    String str_get_type = dedicatedColumn.get_type();
                    String str_get_name = dedicatedColumn.get_name();
                    ArrayList<String> str_array_list = str_array_lists.get(i);

                    // Use the layout from NewDocumentFragment
                    parent_bp[i] = (LinearLayout) inflater.inflate(R.layout.list_item_dedicated_column, null);

                    ll_dedicated_column[i] = parent_bp[i].findViewById(R.id.ll_dedicated_column);
                    txt_key_field[i] = parent_bp[i].findViewById(R.id.txt_key_field);
                    edt_value[i] = parent_bp[i].findViewById(R.id.edt_value);
                    edt_spnr_value[i] = parent_bp[i].findViewById(R.id.edt_spnr_value);
                    img_dialog_item[i] = parent_bp[i].findViewById(R.id.img_dialog_item);
                    ll_spinner_values[i] = parent_bp[i].findViewById(R.id.ll_spinner_values);
                    spnr_value[i] = parent_bp[i].findViewById(R.id.spnr_value);
                    txt_dtp_value[i] = parent_bp[i].findViewById(R.id.txt_dtp_value);
                    chk_value[i] = parent_bp[i].findViewById(R.id.chk_value);
                    autocomplete_txt_value[i] = parent_bp[i].findViewById(R.id.autocomplete_txt_value);
                    img_Scan_Code[i] = parent_bp[i].findViewById(R.id.img_Scan_Code);

                    // Set tags
                    ll_dedicated_column[i].setTag(i);
                    txt_key_field[i].setTag("" + i);
                    edt_value[i].setTag("" + i);
                    img_Scan_Code[i].setTag("" + i);
                    spnr_value[i].setTag("" + i);
                    edt_spnr_value[i].setTag("" + i);
                    img_dialog_item[i].setTag("" + i);
                    ll_spinner_values[i].setTag("" + i);
                    txt_dtp_value[i].setTag("" + i);
                    chk_value[i].setTag("" + i);
                    autocomplete_txt_value[i].setTag("" + i);

                    // Add * for mandatory fields using utility class
                    boolean isMandatory = "1".equalsIgnoreCase(dedicatedColumn.get_mandatory());
                    String displayName = DedicatedColumnsValidator.formatFieldName(str_get_name, isMandatory);
                    txt_key_field[i].setText(displayName);

                    edt_value[i].setInputType(InputType.TYPE_CLASS_TEXT);

                    String columnName = dedicatedColumn.get_column();
                    String existingValue = existingColumnValues.get(columnName);

                    // Handle different field types with data from first pass
                    handleFieldType(i, str_get_type, existingValue, str_array_list);

                    ll_dedicated_column_main.addView(parent_bp[i]);
                }
            }
    }

    private void handleFieldType(int i, String str_get_type, String existingValue, ArrayList<String> str_array_list) {
        // Hide all views initially
            edt_value[i].setVisibility(View.GONE);
            edt_spnr_value[i].setVisibility(View.GONE);
            img_dialog_item[i].setVisibility(View.GONE);
            ll_spinner_values[i].setVisibility(View.GONE);
            spnr_value[i].setVisibility(View.GONE);
            txt_dtp_value[i].setVisibility(View.GONE);
            chk_value[i].setVisibility(View.GONE);
            autocomplete_txt_value[i].setVisibility(View.GONE);
            img_Scan_Code[i].setVisibility(View.GONE);

            if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_STRING)) {
                edt_value[i].setVisibility(View.VISIBLE);
                edt_value[i].setInputType(InputType.TYPE_CLASS_TEXT);
                if (existingValue != null) {
                    edt_value[i].setText(existingValue);
                }
            } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_DECIMAL)) {
                edt_value[i].setVisibility(View.VISIBLE);
                edt_value[i].setInputType(InputType.TYPE_CLASS_NUMBER | InputType.TYPE_NUMBER_FLAG_DECIMAL);
                if (existingValue != null) {
                    edt_value[i].setText(existingValue);
                }
            } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_BOOL)) {
                chk_value[i].setVisibility(View.VISIBLE);
                if (existingValue != null) {
                    chk_value[i].setChecked(Boolean.parseBoolean(existingValue));
                }
            } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_COMBODEF)) {
                spnr_value[i].setVisibility(View.VISIBLE);
                setupComboDefSpinner(i, existingValue, str_array_list);
            } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_DATE)) {
                txt_dtp_value[i].setVisibility(View.VISIBLE);
                setupDateField(i, existingValue);
            }
    }

    private void setupComboDefSpinner(int i, String existingValue, ArrayList<String> str_array_list) {
        // Copy exact logic from NewDocumentFragment lines 3258-3330
        spnr_value[i].setVisibility(Spinner.VISIBLE);

        if (str_array_list != null) {
            if (str_array_list.size() > 0) {

                int selected_value_position = 0;

                // Check for existing value like in NewDocumentFragment
                if (existingValue != null && !existingValue.isEmpty()) {
                    for (int j = 0; j < str_array_list.size(); j++) {
                        String str_spinner_value = str_array_list.get(j);

                        if (str_spinner_value.equalsIgnoreCase(existingValue)) {
                            selected_value_position = j;
                            selected_value_position = selected_value_position + 1;
                            break;
                        }
                    }
                }

                // Create array with empty first element exactly like in NewDocumentFragment
                String[] stockItemsNameList = new String[str_array_list.size() + 1];

                int counter = 0;
                stockItemsNameList[counter] = "";

                for (int j = 0; j < str_array_list.size(); j++) {
                    counter = counter + 1;
                    stockItemsNameList[counter] = str_array_list.get(j);
                }

                // Use SpinnerArrayAdapter exactly like in NewDocumentFragment
                SpinnerArrayAdapter adapter = new SpinnerArrayAdapter(i, getActivity(), R.layout.spinner_item, stockItemsNameList);
                spnr_value[i].setAdapter(adapter);
                spnr_value[i].setSelection(selected_value_position);

                Log.e(LomagApplication.APP_TAG, "setupComboDefSpinner - set adapter with " + stockItemsNameList.length + " items, selected position: " + selected_value_position);
            }
        }
    }

    private void setupDateField(int i, String existingValue) {
        final Calendar calendar = Calendar.getInstance();
            int mYear = calendar.get(Calendar.YEAR);
            int mMonth = calendar.get(Calendar.MONTH);
            int mDay = calendar.get(Calendar.DAY_OF_MONTH);

            String str_mDay = "";
            if (mDay < 10) {
                str_mDay = "0" + mDay;
            } else {
                str_mDay = "" + mDay;
            }

            String str_mMonth = "";
            if ((mMonth + 1) < 10) {
                str_mMonth = "0" + (mMonth + 1);
            } else {
                str_mMonth = "" + (mMonth + 1);
            }

            String currentDate = str_mDay + "." + str_mMonth + "." + mYear;

            if (existingValue != null && !existingValue.isEmpty()) {
                txt_dtp_value[i].setText(existingValue);
            } else {
                txt_dtp_value[i].setText(currentDate);
            }

            // Add click listener for date picker
            final int index = i;
            txt_dtp_value[i].setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    showDatePicker(index);
                }
            });
    }

    private void showDatePicker(final int index) {
        final Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH);
            int day = calendar.get(Calendar.DAY_OF_MONTH);

            android.app.DatePickerDialog datePickerDialog = new android.app.DatePickerDialog(
                getActivity(),
                new android.app.DatePickerDialog.OnDateSetListener() {
                    @Override
                    public void onDateSet(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
                        String str_day = "";
                        if (dayOfMonth < 10) {
                            str_day = "0" + dayOfMonth;
                        } else {
                            str_day = "" + dayOfMonth;
                        }

                        String str_month = "";
                        if ((monthOfYear + 1) < 10) {
                            str_month = "0" + (monthOfYear + 1);
                        } else {
                            str_month = "" + (monthOfYear + 1);
                        }

                        String selectedDate = str_day + "." + str_month + "." + year;
                        txt_dtp_value[index].setText(selectedDate);
                    }
                }, year, month, day);
            datePickerDialog.show();
    }

    private void saveSerialNumberDedicatedColumns() {
        try {
            // First validate mandatory fields
            if (!validateMandatoryFields()) {
                return;
            }

            // Save values using actual column names from DedicatedColumn.get_column() like in AddStockItemProcedure
            for (int i = 0; i < dedicatedColumn_list.size(); i++) {
                DedicatedColumn dedicatedColumn = dedicatedColumn_list.get(i);
                String str_get_type = dedicatedColumn.get_type();
                String columnName = dedicatedColumn.get_column();
                String value = "";

                if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_STRING)) {
                    value = edt_value[i].getText().toString();
                } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_DECIMAL)) {
                    value = edt_value[i].getText().toString();
                } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_BOOL)) {
                    value = String.valueOf(chk_value[i].isChecked());
                } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_COMBODEF)) {
                    value = String.valueOf(spnr_value[i].getSelectedItem());
                } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_DATE)) {
                    value = txt_dtp_value[i].getText().toString();
                }

                if (value != null && !value.trim().isEmpty()) {
                    serialNumber.setDedicatedColumnValue(columnName, value);
                }
            }

            mListener.onSaveSerialNumberDedicatedColumns(serialNumber);
        } catch (Exception e) {
            Log.e(LomagApplication.APP_TAG, "saveSerialNumberDedicatedColumns error: " + e);
        }
    }

    private boolean validateMandatoryFields() {
        for (int i = 0; i < dedicatedColumn_list.size(); i++) {
            DedicatedColumn dedicatedColumn = dedicatedColumn_list.get(i);

            if ("1".equalsIgnoreCase(dedicatedColumn.get_mandatory())) {
                String str_get_type = dedicatedColumn.get_type();
                String value = "";
                boolean isEmpty = false;

                if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_STRING)) {
                    value = edt_value[i].getText().toString();
                    isEmpty = value == null || value.trim().isEmpty();
                } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_DECIMAL)) {
                    value = edt_value[i].getText().toString();
                    isEmpty = value == null || value.trim().isEmpty();
                } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_BOOL)) {
                    // Boolean fields are never empty (always true or false)
                    isEmpty = false;
                } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_COMBODEF)) {
                    value = String.valueOf(spnr_value[i].getSelectedItem());
                    isEmpty = value == null || value.trim().isEmpty() || "null".equals(value);
                } else if (str_get_type.equalsIgnoreCase(DedicatedColumn.DedicatedColumn_DATA_TYPE_DATE)) {
                    value = txt_dtp_value[i].getText().toString();
                    isEmpty = value == null || value.trim().isEmpty();
                }

                // If mandatory field is empty, show error
                if (isEmpty) {
                    DedicatedColumnsValidator.showMandatoryDedicatedColumnError(dedicatedColumn.get_name(), serialNumber.getSerialNr(), getActivity());
                    return false;
                }
            }
        }
        return true;
    }

}
