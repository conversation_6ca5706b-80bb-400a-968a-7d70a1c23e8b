<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AndroidLayouts">
    <shared>
      <config />
    </shared>
    <layouts>
      <layout url="file://$USER_HOME$/AppData/Local/Android/Sdk/platforms/android-31/data/res/layout/simple_list_item_1.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/edittext_background.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/drawable/flat_button_background.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/dialog_new_serial_nr.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/footer_list_loading.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/footer_serial_list.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_actual_wares_by_document.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_add_serial_numbers.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_add_warehouse_item.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_choose_serial_numbers.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_correction_invoice_list.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_interbranch_transfer_new_document.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_interbranch_transfer_new_element.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_interbranch_transfer_serial_nr_new_element.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_invoice_add_articles.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_main_menu.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_new_doc_element_dialog.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_new_document.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_new_invoice.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_new_order.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_order_add_articles.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_rental.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/fragment_return_rental_add_articles.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/header_add_doc_elements.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/list_item_change_location_preview_item.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/list_item_document_element.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/list_item_receipt_preview_item.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/list_item_select_picking_item.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/list_item_select_supplies.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/list_item_serial.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/layout/list_item_stock_element_for_document.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
      <layout url="file://$PROJECT_DIR$/app/src/main/res/xml/preferances.xml">
        <config>
          <theme>@style/Theme.AppCompat.Light.NoActionBar</theme>
        </config>
      </layout>
    </layouts>
  </component>
  <component name="AndroidLogFilters">
    <option name="TOOL_WINDOW_LOG_LEVEL" value="verbose" />
    <option name="TOOL_WINDOW_CONFIGURED_FILTER" value="No Filters" />
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="64807fdf-eb78-4244-b498-e931ac31e62b" name="Default Changelist" comment="Wersja 1.4.27">
      <change beforePath="$PROJECT_DIR$/app/build.gradle" beforeDir="false" afterPath="$PROJECT_DIR$/app/build.gradle" afterDir="false" />
    </list>
    <list id="30221694-105e-4bff-af9b-f5c8667917c0" name="ignore" comment="">
      <change afterPath="$PROJECT_DIR$/.idea/caches/deviceStreaming.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/migrations.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/gradle.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/gradle.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/runConfigurations.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/runConfigurations.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/local.properties" beforeDir="false" afterPath="$PROJECT_DIR$/local.properties" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="CommittedChangesCache">
    <option name="initialCount" value="100" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="device_and_snapshot_combo_box_target[DeviceId(pluginId=PhysicalDevice, isTemplate=false, identifier=serial=ZY22JFZFZC)]" />
  <component name="ExternalProjectsData">
    <projectState path="$PROJECT_DIR$">
      <ProjectState />
    </projectState>
  </component>
  <component name="ExternalProjectsManager">
    <system id="GRADLE">
      <state>
        <task path="$PROJECT_DIR$">
          <activation />
        </task>
        <projects_view>
          <tree_state>
            <expand />
            <select />
          </tree_state>
        </projects_view>
      </state>
    </system>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="GenerateSignedApkSettings">
    <option name="KEY_STORE_PATH" value="C:\vs\LomagWebAndroidStudio\lomag_key" />
    <option name="KEY_ALIAS" value="lomag" />
    <option name="REMEMBER_PASSWORDS" value="true" />
    <option name="BUILD_TARGET_KEY" value="apk" />
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/app/libs/jtds-1.2.7.jar!/net/sourceforge/jtds/jdbc/ConnectionJDBC2.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="InstancesTracker">
    <option name="classes">
      <map>
        <entry key="android.graphics.Bitmap" value="CREATION" />
      </map>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="1xVPziJPftcQNWySbcMzNG335N6" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Android App.app.executor&quot;: &quot;Debug&quot;,
    &quot;ApkExportedModule&quot;: &quot;LomagWebAndroidStudio.app&quot;,
    &quot;DEBUGGABLE_DEVICE&quot;: &quot;samsung-sm_a037g-R9YRB0Q8ZCA&quot;,
    &quot;DEBUGGABLE_PROCESS&quot;: &quot;com.longint.lomag.lomagweb&quot;,
    &quot;DEBUGGER_ID&quot;: &quot;Auto&quot;,
    &quot;ExportApk.ApkPathForLomagWebAndroidStudio.app&quot;: &quot;C:\\vs\\LomagWebAndroidStudio\\app&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder0&quot;: &quot;0&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder1&quot;: &quot;1&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder2&quot;: &quot;2&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder3&quot;: &quot;3&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder4&quot;: &quot;4&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder5&quot;: &quot;5&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth0&quot;: &quot;298&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth1&quot;: &quot;298&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth2&quot;: &quot;298&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth3&quot;: &quot;298&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth4&quot;: &quot;299&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth5&quot;: &quot;298&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder0&quot;: &quot;0&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder1&quot;: &quot;1&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder2&quot;: &quot;2&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder3&quot;: &quot;3&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder4&quot;: &quot;4&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder5&quot;: &quot;5&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth0&quot;: &quot;298&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth1&quot;: &quot;298&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth2&quot;: &quot;298&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth3&quot;: &quot;298&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth4&quot;: &quot;299&quot;,
    &quot;FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth5&quot;: &quot;298&quot;,
    &quot;Gradle.Download Sources.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.LomagWebAndroidStudio.executor&quot;: &quot;Run&quot;,
    &quot;Gradle.Upgrade Gradle wrapper.executor&quot;: &quot;Run&quot;,
    &quot;PROJECT_TRUSTED_KEY&quot;: &quot;true&quot;,
    &quot;ResourceManagerPrefKey.ResourceType&quot;: &quot;COLOR&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.readMode.enableVisualFormatting&quot;: &quot;true&quot;,
    &quot;SHOW_ALL_PROCESSES&quot;: &quot;false&quot;,
    &quot;android-custom-viewC:/Users/<USER>/AppData/Local/Android/Sdk/sources/android-31/android/view/View.java_SELECTED&quot;: &quot;View&quot;,
    &quot;android-custom-viewC:/Users/<USER>/AppData/Local/Android/Sdk/sources/android-31/android/widget/AbsSpinner.java_SELECTED&quot;: &quot;AbsSpinner&quot;,
    &quot;android-custom-viewC:/Users/<USER>/AppData/Local/Android/Sdk/sources/android-31/android/widget/AutoCompleteTextView.java_SELECTED&quot;: &quot;AutoCompleteTextView&quot;,
    &quot;android-custom-viewC:/Users/<USER>/AppData/Local/Android/Sdk/sources/android-31/android/widget/LinearLayout.java_SELECTED&quot;: &quot;LinearLayout&quot;,
    &quot;android-custom-viewC:/Users/<USER>/AppData/Local/Android/Sdk/sources/android-31/android/widget/TextView.java_SELECTED&quot;: &quot;TextView&quot;,
    &quot;android-custom-viewC:/Users/<USER>/AppData/Local/Android/Sdk/sources/android-33/android/widget/TextView.java_SELECTED&quot;: &quot;TextView&quot;,
    &quot;android-custom-viewC:/vs/gradle-5.6.4/caches/modules-2/files-2.1/androidx.appcompat/appcompat/1.0.2/e38e7c85994112b70d4548176128c72b8477c110/appcompat-1.0.2-sources.jar!/androidx/appcompat/widget/AppCompatEditText.java_SELECTED&quot;: &quot;AppCompatEditText&quot;,
    &quot;cf.first.check.clang-format&quot;: &quot;false&quot;,
    &quot;cidr.known.project.marker&quot;: &quot;true&quot;,
    &quot;com.google.services.firebase.aqiPopupShown&quot;: &quot;true&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/vs/LomagWebAndroidStudio&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.17&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.android.studio.ml.bot.mainConfigurable&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;ChangesTree.GroupingKeys&quot;: [],
    &quot;ExportApk.BuildVariants&quot;: [
      &quot;release&quot;
    ]
  }
}</component>
  <component name="PsdUISettings">
    <option name="MODULE_TAB" value="Default Config" />
    <option name="LAST_EDITED_SIGNING_CONFIG" value="debug" />
    <option name="LAST_EDITED_BUILD_TYPE" value="release" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\vs\LomagWebAndroidStudio\app\src\main\res\values-de" />
      <recent name="C:\vs\LomagWebAndroidStudio\app\src\main\res\layout" />
      <recent name="C:\vs\LomagWebAndroidStudio\app\src\main\java\com\longint\lomag\lomagweb\utils" />
      <recent name="C:\vs\LomagWebAndroidStudio\app\src\main\res\menu" />
      <recent name="C:\vs\LomagWebAndroidStudio\app\src\main\java\com\longint\lomag\lomagweb" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.longint.lomag.lomagweb.db.procedures.add" />
      <recent name="com.longint.lomag.lomagweb" />
      <recent name="com.longint.lomag.lomagweb.fragments" />
      <recent name="com.longint.lomag.lomagweb.db.procedures.get" />
      <recent name="com.longint.lomag.lomagweb.db.toobjects" />
    </key>
  </component>
  <component name="RunManager" selected="Android App.app">
    <configuration name="app" type="AndroidRunConfigurationType" factoryName="Android App" activateToolWindowBeforeRun="false">
      <module name="LomagWebAndroidStudio.app" />
      <option name="ANDROID_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="DEPLOY" value="true" />
      <option name="DEPLOY_APK_FROM_BUNDLE" value="false" />
      <option name="DEPLOY_AS_INSTANT" value="false" />
      <option name="ARTIFACT_NAME" value="" />
      <option name="PM_INSTALL_OPTIONS" value="" />
      <option name="ALL_USERS" value="false" />
      <option name="ALWAYS_INSTALL_WITH_PM" value="false" />
      <option name="ALLOW_ASSUME_VERIFIED" value="false" />
      <option name="CLEAR_APP_STORAGE" value="false" />
      <option name="DYNAMIC_FEATURES_DISABLED_LIST" value="" />
      <option name="ACTIVITY_EXTRA_FLAGS" value="" />
      <option name="MODE" value="default_activity" />
      <option name="RESTORE_ENABLED" value="false" />
      <option name="RESTORE_FILE" value="" />
      <option name="RESTORE_FRESH_INSTALL_ONLY" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <option name="DEEP_LINK" value="" />
      <option name="ACTIVITY" value="" />
      <option name="ACTIVITY_CLASS" value="" />
      <option name="SEARCH_ACTIVITY_IN_GLOBAL_SCOPE" value="false" />
      <option name="SKIP_ACTIVITY_VALIDATION" value="false" />
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="All Tests" type="AndroidTestRunConfigurationType" factoryName="Android Instrumented Tests" temporary="true" nameIsGenerated="true">
      <module name="LomagWebAndroidStudio.app" />
      <option name="ANDROID_TEST_RUN_CONFIGURATION_SCHEMA_VERSION" value="1" />
      <option name="TESTING_TYPE" value="0" />
      <option name="METHOD_NAME" value="" />
      <option name="CLASS_NAME" value="" />
      <option name="PACKAGE_NAME" value="" />
      <option name="TEST_NAME_REGEX" value="" />
      <option name="INSTRUMENTATION_RUNNER_CLASS" value="" />
      <option name="EXTRA_OPTIONS" value="" />
      <option name="RETENTION_ENABLED" value="No" />
      <option name="RETENTION_MAX_SNAPSHOTS" value="2" />
      <option name="RETENTION_COMPRESS_SNAPSHOTS" value="false" />
      <option name="CLEAR_LOGCAT" value="false" />
      <option name="SHOW_LOGCAT_AUTOMATICALLY" value="false" />
      <option name="TARGET_SELECTION_MODE" value="DEVICE_AND_SNAPSHOT_COMBO_BOX" />
      <option name="SELECTED_CLOUD_MATRIX_CONFIGURATION_ID" value="-1" />
      <option name="SELECTED_CLOUD_MATRIX_PROJECT_ID" value="" />
      <option name="DEBUGGER_TYPE" value="Auto" />
      <Auto>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Auto>
      <Hybrid>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Hybrid>
      <Java>
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Java>
      <Native>
        <option name="USE_JAVA_AWARE_DEBUGGER" value="false" />
        <option name="SHOW_STATIC_VARS" value="true" />
        <option name="WORKING_DIR" value="" />
        <option name="TARGET_LOGGING_CHANNELS" value="lldb process:gdb-remote packets" />
        <option name="SHOW_OPTIMIZED_WARNING" value="true" />
        <option name="ATTACH_ON_WAIT_FOR_DEBUGGER" value="false" />
        <option name="DEBUG_SANDBOX_SDK" value="false" />
      </Native>
      <Profilers>
        <option name="ADVANCED_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_ENABLED" value="false" />
        <option name="STARTUP_CPU_PROFILING_CONFIGURATION_NAME" value="Java/Kotlin Method Sample (legacy)" />
        <option name="STARTUP_NATIVE_MEMORY_PROFILING_ENABLED" value="false" />
        <option name="NATIVE_MEMORY_SAMPLE_RATE_BYTES" value="2048" />
      </Profilers>
      <method v="2">
        <option name="Android.Gradle.BeforeRunTask" enabled="true" />
      </method>
    </configuration>
    <configuration name="LomagWebAndroidStudio" type="GradleRunConfiguration" factoryName="Gradle">
      <ExternalSystemSettings>
        <option name="executionName" />
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="externalSystemIdString" value="GRADLE" />
        <option name="scriptParameters" value="" />
        <option name="taskDescriptions">
          <list />
        </option>
        <option name="taskNames">
          <list />
        </option>
        <option name="vmOptions" />
      </ExternalSystemSettings>
      <ExternalSystemDebugServerProcess>true</ExternalSystemDebugServerProcess>
      <ExternalSystemReattachDebugProcess>true</ExternalSystemReattachDebugProcess>
      <DebugAllEnabled>false</DebugAllEnabled>
      <RunAsTest>false</RunAsTest>
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Android App.app" />
      <item itemvalue="Android Instrumented Tests.All Tests" />
      <item itemvalue="Gradle.LomagWebAndroidStudio" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Android Instrumented Tests.All Tests" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="project-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\vs\LomagWebAndroidStudio" />
          <option name="myCopyRoot" value="C:\vs\LomagWebAndroidStudio" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\vs\LomagWebAndroidStudio" />
          <option name="myCopyRoot" value="C:\vs\LomagWebAndroidStudio" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="64807fdf-eb78-4244-b498-e931ac31e62b" name="Default Changelist" comment="" />
      <changelist id="30221694-105e-4bff-af9b-f5c8667917c0" name="ignore" comment="" />
      <created>1630437547545</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1630437547545</updated>
    </task>
    <task id="LOCAL-00002" summary="Wersja 1.3.09">
      <created>1633627394255</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1633627394255</updated>
    </task>
    <task id="LOCAL-00003" summary="Wersja 1.3.10">
      <created>1634917022510</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1634917022511</updated>
    </task>
    <task id="LOCAL-00004" summary="Wersja 1.3.10 nowe elementy">
      <created>1634917110691</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1634917110691</updated>
    </task>
    <task id="LOCAL-00005" summary="Wersja 1.3.11">
      <created>1635364514677</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1635364514677</updated>
    </task>
    <task id="LOCAL-00006" summary="Wersja 1.3.12">
      <created>1636192721379</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1636192721379</updated>
    </task>
    <task id="LOCAL-00007" summary="Wersja 1.3.12 nowe elementy">
      <created>1636798115427</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1636798115427</updated>
    </task>
    <task id="LOCAL-00008" summary="Wersja 1.3.13">
      <created>1637099140325</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1637099140325</updated>
    </task>
    <task id="LOCAL-00009" summary="Wersja 1.3.13 poprawa">
      <created>1637142869844</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1637142869844</updated>
    </task>
    <task id="LOCAL-00010" summary="Wersja 1.3.15">
      <created>1637869279801</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1637869279801</updated>
    </task>
    <task id="LOCAL-00011" summary="Wersja 1.3.16">
      <created>1638223463041</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1638223463041</updated>
    </task>
    <task id="LOCAL-00012" summary="Wersja 1.3.17">
      <created>1638811408434</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1638811408434</updated>
    </task>
    <task id="LOCAL-00013" summary="Wersja 1.3.17">
      <created>1639551996612</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1639551996612</updated>
    </task>
    <task id="LOCAL-00014" summary="Wersja 1.3.17">
      <created>1640117861733</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1640117861733</updated>
    </task>
    <task id="LOCAL-00015" summary="Wersja 1.3.17 cd">
      <created>1640934131259</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1640934131259</updated>
    </task>
    <task id="LOCAL-00016" summary="Wersja 1.3.18">
      <created>1641399957504</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1641399957504</updated>
    </task>
    <task id="LOCAL-00017" summary="Wersja 1.3.18 cd">
      <created>1642611457369</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1642611457369</updated>
    </task>
    <task id="LOCAL-00018" summary="Wersja 1.3.21">
      <created>1644056078855</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1644056078855</updated>
    </task>
    <task id="LOCAL-00019" summary="Wersja 1.3.25">
      <created>1645217192935</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1645217192935</updated>
    </task>
    <task id="LOCAL-00020" summary="Wersja 1.3.26">
      <created>1645646791292</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1645646791292</updated>
    </task>
    <task id="LOCAL-00021" summary="Wersja 1.3.27">
      <created>1646601725130</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1646601725131</updated>
    </task>
    <task id="LOCAL-00022" summary="Wersja 1.3.28">
      <created>1647410512575</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1647410512575</updated>
    </task>
    <task id="LOCAL-00023" summary="Wersja 1.3.29">
      <created>1648581553213</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1648581553213</updated>
    </task>
    <task id="LOCAL-00024" summary="Wersja 1.3.30">
      <created>1652476286663</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1652476286663</updated>
    </task>
    <task id="LOCAL-00025" summary="Wersja 1.3.31">
      <created>1653206416901</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1653206416901</updated>
    </task>
    <task id="LOCAL-00026" summary="Wersja 1.3.32">
      <created>1654117675564</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1654117675564</updated>
    </task>
    <task id="LOCAL-00027" summary="Wersja 1.3.34">
      <created>1659256030345</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1659256030345</updated>
    </task>
    <task id="LOCAL-00028" summary="Wersja 1.3.36">
      <created>1661587493432</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1661587493432</updated>
    </task>
    <task id="LOCAL-00029" summary="Wersja 1.3.37">
      <created>1665175119316</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1665175119316</updated>
    </task>
    <task id="LOCAL-00030" summary="Wersja 1.3.39">
      <created>1667071045848</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1667071045848</updated>
    </task>
    <task id="LOCAL-00031" summary="Wersja 1.3.41">
      <created>1669317956085</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1669317956085</updated>
    </task>
    <task id="LOCAL-00032" summary="Wersja 1.3.42">
      <created>1670018911240</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1670018911240</updated>
    </task>
    <task id="LOCAL-00033" summary="Wersja 1.3.43">
      <created>1671047500617</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1671047500617</updated>
    </task>
    <task id="LOCAL-00034" summary="Wersja 1.3.44">
      <created>1673381628623</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1673381628623</updated>
    </task>
    <task id="LOCAL-00035" summary="Wersja 1.3.46">
      <created>1674160744234</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1674160744234</updated>
    </task>
    <task id="LOCAL-00036" summary="Wersja 1.3.47">
      <created>1674675719189</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1674675719189</updated>
    </task>
    <task id="LOCAL-00037" summary="Wersja 1.3.47 poprawa">
      <created>1675529085805</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1675529085806</updated>
    </task>
    <task id="LOCAL-00038" summary="Wersja 1.3.48">
      <created>1676215042564</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1676215042564</updated>
    </task>
    <task id="LOCAL-00039" summary="Wersja 1.3.49">
      <created>1676495851268</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1676495851268</updated>
    </task>
    <task id="LOCAL-00040" summary="Wersja 1.3.50">
      <created>1677519835313</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1677519835313</updated>
    </task>
    <task id="LOCAL-00041" summary="Wersja 1.3.52">
      <created>1679162063025</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1679162063025</updated>
    </task>
    <task id="LOCAL-00042" summary="Wersja 1.3.52 poprawa">
      <created>1679162615401</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1679162615401</updated>
    </task>
    <task id="LOCAL-00043" summary="Wersja 1.3.53">
      <created>1679606639348</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1679606639348</updated>
    </task>
    <task id="LOCAL-00044" summary="Wersja 1.3.55">
      <created>1679940960415</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1679940960415</updated>
    </task>
    <task id="LOCAL-00045" summary="Wersja 1.3.62">
      <created>1681500549043</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1681500549043</updated>
    </task>
    <task id="LOCAL-00046" summary="Wersja 1.3.63">
      <created>1682614691825</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1682614691825</updated>
    </task>
    <task id="LOCAL-00047" summary="Wersja 1.3.65">
      <created>1682915229696</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1682915229696</updated>
    </task>
    <task id="LOCAL-00048" summary="Wersja 1.3.65">
      <created>1682915268945</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1682915268945</updated>
    </task>
    <task id="LOCAL-00049" summary="Wersja 1.3.66">
      <created>1686162852360</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1686162852360</updated>
    </task>
    <task id="LOCAL-00050" summary="Wersja 1.3.71">
      <created>1697895433550</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1697895433550</updated>
    </task>
    <option name="localTasksCounter" value="75" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="Wersja 1.3.65" />
    <MESSAGE value="Wersja 1.3.66" />
    <MESSAGE value="Wersja 1.3.73" />
    <MESSAGE value="Wersja 1.3.74" />
    <MESSAGE value="Wersja 1.3.75" />
    <MESSAGE value="Wersja 1.3.71" />
    <MESSAGE value="Wersja 1.3.84" />
    <MESSAGE value="Wersja 1.3.87" />
    <MESSAGE value="Wersja 1.3.90" />
    <MESSAGE value="Wersja 1.3.89" />
    <MESSAGE value="Wersja 1.3.94" />
    <MESSAGE value="Wersja 1.3.97" />
    <MESSAGE value="Wersja 1.4.02" />
    <MESSAGE value="Wersja 1.3.99" />
    <MESSAGE value="Wersja 1.4.07" />
    <MESSAGE value="Wersja 1.4.08" />
    <MESSAGE value="Wersja 1.4.17" />
    <MESSAGE value="Wersja 1.4.11" />
    <MESSAGE value="Wersja 1.4.18" />
    <MESSAGE value="Wersja 1.4.20" />
    <MESSAGE value="Wersja 1.4.23" />
    <MESSAGE value="Wersja 1.4.24" />
    <MESSAGE value="Wersja 1.4.25" />
    <MESSAGE value="Wersja 1.4.26" />
    <MESSAGE value="Wersja 1.4.27" />
    <option name="LAST_COMMIT_MESSAGE" value="Wersja 1.4.27" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment.java</url>
          <line>659</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment.java</url>
          <line>651</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewInvoiceArticlesFragment.java</url>
          <line>549</line>
          <option name="timeStamp" value="21" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment_OLD.java</url>
          <line>1362</line>
          <option name="timeStamp" value="32" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/adapters/ReceiptsListAdapter.java</url>
          <line>67</line>
          <option name="timeStamp" value="313" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>146</line>
          <option name="timeStamp" value="520" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>904</line>
          <option name="timeStamp" value="530" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1189</line>
          <option name="timeStamp" value="533" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>214</line>
          <option name="timeStamp" value="537" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>218</line>
          <option name="timeStamp" value="543" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>399</line>
          <option name="timeStamp" value="591" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>923</line>
          <option name="timeStamp" value="623" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>jar://$USER_HOME$/AppData/Local/Android/Sdk/platforms/android-30/android.jar!/android/widget/EditText.class</url>
          <line>52</line>
          <option name="timeStamp" value="640" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>185</line>
          <option name="timeStamp" value="685" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/toobjects/StockItem.java</url>
          <line>115</line>
          <option name="timeStamp" value="701" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>1438</line>
          <option name="timeStamp" value="750" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>1448</line>
          <option name="timeStamp" value="751" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>456</line>
          <option name="timeStamp" value="800" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1216</line>
          <option name="timeStamp" value="804" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1493</line>
          <option name="timeStamp" value="805" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>225</line>
          <option name="timeStamp" value="808" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>444</line>
          <option name="timeStamp" value="809" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>483</line>
          <option name="timeStamp" value="810" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>520</line>
          <option name="timeStamp" value="811" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>560</line>
          <option name="timeStamp" value="812" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1068</line>
          <option name="timeStamp" value="817" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1157</line>
          <option name="timeStamp" value="819" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1492</line>
          <option name="timeStamp" value="823" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1606</line>
          <option name="timeStamp" value="825" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>945</line>
          <option name="timeStamp" value="827" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>417</line>
          <option name="timeStamp" value="830" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1476</line>
          <option name="timeStamp" value="834" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1347</line>
          <option name="timeStamp" value="844" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>127</line>
          <option name="timeStamp" value="847" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment_OLD.java</url>
          <line>1536</line>
          <option name="timeStamp" value="882" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>758</line>
          <option name="timeStamp" value="938" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/WarehousePreviewFragment.java</url>
          <line>161</line>
          <option name="timeStamp" value="956" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/toobjects/StockItem.java</url>
          <line>131</line>
          <option name="timeStamp" value="960" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewDocumentFragment.java</url>
          <line>1244</line>
          <option name="timeStamp" value="1010" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>401</line>
          <option name="timeStamp" value="1212" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1707</line>
          <option name="timeStamp" value="1332" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1734</line>
          <option name="timeStamp" value="1338" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1602</line>
          <option name="timeStamp" value="1343" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1463</line>
          <option name="timeStamp" value="1347" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1446</line>
          <option name="timeStamp" value="1348" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1438</line>
          <option name="timeStamp" value="1349" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1409</line>
          <option name="timeStamp" value="1350" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>197</line>
          <option name="timeStamp" value="1356" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>175</line>
          <option name="timeStamp" value="1357" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>169</line>
          <option name="timeStamp" value="1358" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>164</line>
          <option name="timeStamp" value="1359" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>159</line>
          <option name="timeStamp" value="1360" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>154</line>
          <option name="timeStamp" value="1361" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>983</line>
          <option name="timeStamp" value="1384" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1370</line>
          <option name="timeStamp" value="1388" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>2067</line>
          <option name="timeStamp" value="1483" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>2151</line>
          <option name="timeStamp" value="1484" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>959</line>
          <option name="timeStamp" value="1547" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/SendLogActivity.java</url>
          <line>187</line>
          <option name="timeStamp" value="1567" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3058</line>
          <option name="timeStamp" value="1814" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/utils/UnCaughtException.java</url>
          <line>126</line>
          <option name="timeStamp" value="1887" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>2044</line>
          <option name="timeStamp" value="1949" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddDocumentElementProcedure.java</url>
          <line>1143</line>
          <option name="timeStamp" value="1971" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4605</line>
          <option name="timeStamp" value="1984" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddSerialNumbersFragment.java</url>
          <line>549</line>
          <option name="timeStamp" value="1997" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1583</line>
          <option name="timeStamp" value="2114" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4690</line>
          <option name="timeStamp" value="2116" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>1478</line>
          <option name="timeStamp" value="2122" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment_OLD.java</url>
          <line>1564</line>
          <option name="timeStamp" value="2123" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>2061</line>
          <option name="timeStamp" value="2212" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3379</line>
          <option name="timeStamp" value="2213" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewOrderArticlesFragment.java</url>
          <line>629</line>
          <option name="timeStamp" value="2292" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewOrderArticlesFragment.java</url>
          <line>623</line>
          <option name="timeStamp" value="2293" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4500</line>
          <option name="timeStamp" value="2312" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4349</line>
          <option name="timeStamp" value="2323" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4354</line>
          <option name="timeStamp" value="2324" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewOrderFragment.java</url>
          <line>736</line>
          <option name="timeStamp" value="2335" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/SendLogActivity.java</url>
          <line>206</line>
          <option name="timeStamp" value="2460" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/SendLogActivity.java</url>
          <line>212</line>
          <option name="timeStamp" value="2463" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/SendLogActivity.java</url>
          <line>235</line>
          <option name="timeStamp" value="2464" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/SendLogActivity.java</url>
          <line>238</line>
          <option name="timeStamp" value="2465" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/SendLogActivity.java</url>
          <line>261</line>
          <option name="timeStamp" value="2466" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/SendLogActivity.java</url>
          <line>133</line>
          <option name="timeStamp" value="2467" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/utils/UnCaughtException.java</url>
          <line>161</line>
          <option name="timeStamp" value="2509" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1913</line>
          <option name="timeStamp" value="2517" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/SendLogActivity.java</url>
          <line>260</line>
          <option name="timeStamp" value="2539" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/SendLogActivity.java</url>
          <line>89</line>
          <option name="timeStamp" value="2540" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/SendLogActivity.java</url>
          <line>151</line>
          <option name="timeStamp" value="2541" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/SendLogActivity.java</url>
          <line>116</line>
          <option name="timeStamp" value="2542" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4696</line>
          <option name="timeStamp" value="2553" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>511</line>
          <option name="timeStamp" value="2584" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment_OLD.java</url>
          <line>1428</line>
          <option name="timeStamp" value="2599" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/CorrectionInvoiceLineListFragment.java</url>
          <line>339</line>
          <option name="timeStamp" value="2635" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/CorrectionInvoiceLineListFragment.java</url>
          <line>204</line>
          <option name="timeStamp" value="2637" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/CorrectionInvoiceLineListFragment.java</url>
          <line>334</line>
          <option name="timeStamp" value="2638" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>495</line>
          <option name="timeStamp" value="2654" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/zxinglib/src/main/java/jim/h/common/android/lib/zxing/camera/PlanarYUVLuminanceSource.java</url>
          <line>136</line>
          <option name="timeStamp" value="2694" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/WarehousePreviewFragment.java</url>
          <line>110</line>
          <option name="timeStamp" value="2747" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment.java</url>
          <line>994</line>
          <option name="timeStamp" value="2768" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/utils/ListAbstractDataLoader.java</url>
          <line>20</line>
          <option name="timeStamp" value="2778" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/adapters/WarehouseItemsForDocumentAdapter.java</url>
          <line>386</line>
          <option name="timeStamp" value="2799" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/adapters/WarehouseItemsForDocumentAdapter.java</url>
          <line>372</line>
          <option name="timeStamp" value="2800" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>1976</line>
          <option name="timeStamp" value="2847" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>668</line>
          <option name="timeStamp" value="2860" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewOrderFragment.java</url>
          <line>734</line>
          <option name="timeStamp" value="2873" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1908</line>
          <option name="timeStamp" value="2876" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4332</line>
          <option name="timeStamp" value="2879" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewOrderFragment.java</url>
          <line>728</line>
          <option name="timeStamp" value="2946" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewReturn_RentalArticlesFragment.java</url>
          <line>1153</line>
          <option name="timeStamp" value="3041" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewReturn_RentalArticlesFragment.java</url>
          <line>1171</line>
          <option name="timeStamp" value="3042" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewReturn_RentalArticlesFragment.java</url>
          <line>1462</line>
          <option name="timeStamp" value="3043" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewReturn_RentalArticlesFragment.java</url>
          <line>1499</line>
          <option name="timeStamp" value="3044" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1277</line>
          <option name="timeStamp" value="3048" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>1805</line>
          <option name="timeStamp" value="3051" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>1669</line>
          <option name="timeStamp" value="3109" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>903</line>
          <option name="timeStamp" value="3110" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>517</line>
          <option name="timeStamp" value="3143" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>515</line>
          <option name="timeStamp" value="3156" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1922</line>
          <option name="timeStamp" value="3165" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3157</line>
          <option name="timeStamp" value="3183" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>828</line>
          <option name="timeStamp" value="3186" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewInvoiceArticlesFragment.java</url>
          <line>524</line>
          <option name="timeStamp" value="3188" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewInvoiceArticlesFragment.java</url>
          <line>999</line>
          <option name="timeStamp" value="3189" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewOrderArticlesFragment.java</url>
          <line>464</line>
          <option name="timeStamp" value="3190" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment.java</url>
          <line>668</line>
          <option name="timeStamp" value="3192" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewReturn_RentalArticlesFragment.java</url>
          <line>1249</line>
          <option name="timeStamp" value="3194" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewReturn_RentalArticlesFragment.java</url>
          <line>1735</line>
          <option name="timeStamp" value="3195" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3132</line>
          <option name="timeStamp" value="3198" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3149</line>
          <option name="timeStamp" value="3199" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1062</line>
          <option name="timeStamp" value="3201" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>787</line>
          <option name="timeStamp" value="3221" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>363</line>
          <option name="timeStamp" value="3240" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/delete/DeleteDocumentProcedure.java</url>
          <line>40</line>
          <option name="timeStamp" value="3249" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1328</line>
          <option name="timeStamp" value="3262" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>775</line>
          <option name="timeStamp" value="3264" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>797</line>
          <option name="timeStamp" value="3266" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>812</line>
          <option name="timeStamp" value="3267" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1247</line>
          <option name="timeStamp" value="3283" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3722</line>
          <option name="timeStamp" value="3308" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment_OLD.java</url>
          <line>423</line>
          <option name="timeStamp" value="3353" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment_OLD.java</url>
          <line>2184</line>
          <option name="timeStamp" value="3354" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddItemFragment.java</url>
          <line>2509</line>
          <option name="timeStamp" value="3364" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3731</line>
          <option name="timeStamp" value="3380" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3644</line>
          <option name="timeStamp" value="3381" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3889</line>
          <option name="timeStamp" value="3382" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4000</line>
          <option name="timeStamp" value="3383" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4046</line>
          <option name="timeStamp" value="3384" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4093</line>
          <option name="timeStamp" value="3385" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment_OLD.java</url>
          <line>495</line>
          <option name="timeStamp" value="3396" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>1909</line>
          <option name="timeStamp" value="3398" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/SaveArticleShiftElement.java</url>
          <line>343</line>
          <option name="timeStamp" value="3402" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddInterbranchTransferDocumentProcedure.java</url>
          <line>43</line>
          <option name="timeStamp" value="3411" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>386</line>
          <option name="timeStamp" value="3420" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1589</line>
          <option name="timeStamp" value="3421" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4913</line>
          <option name="timeStamp" value="3424" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1059</line>
          <option name="timeStamp" value="3427" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1148</line>
          <option name="timeStamp" value="3428" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewReturn_RentalArticlesFragment.java</url>
          <line>1124</line>
          <option name="timeStamp" value="3429" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3067</line>
          <option name="timeStamp" value="3437" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4921</line>
          <option name="timeStamp" value="3456" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3444</line>
          <option name="timeStamp" value="3466" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4882</line>
          <option name="timeStamp" value="3469" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4899</line>
          <option name="timeStamp" value="3470" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>5118</line>
          <option name="timeStamp" value="3478" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>321</line>
          <option name="timeStamp" value="3485" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>768</line>
          <option name="timeStamp" value="3486" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3118</line>
          <option name="timeStamp" value="3489" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/gs1/ElementStrings.java</url>
          <line>320</line>
          <option name="timeStamp" value="3497" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/gs1/ElementStrings.java</url>
          <line>348</line>
          <option name="timeStamp" value="3505" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4825</line>
          <option name="timeStamp" value="3508" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>957</line>
          <option name="timeStamp" value="3525" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>938</line>
          <option name="timeStamp" value="3526" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>1337</line>
          <option name="timeStamp" value="3535" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment_OLD.java</url>
          <line>621</line>
          <option name="timeStamp" value="3536" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewRentalArticlesFragment_OLD.java</url>
          <line>1460</line>
          <option name="timeStamp" value="3537" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/get/GetItemDeliveryProcedure.java</url>
          <line>170</line>
          <option name="timeStamp" value="3549" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/get/GetItemDeliveryProcedure.java</url>
          <line>164</line>
          <option name="timeStamp" value="3550" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1577</line>
          <option name="timeStamp" value="3555" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>398</line>
          <option name="timeStamp" value="3556" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddDocumentElementProcedure.java</url>
          <line>325</line>
          <option name="timeStamp" value="3558" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddDocumentElementProcedure.java</url>
          <line>378</line>
          <option name="timeStamp" value="3559" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>960</line>
          <option name="timeStamp" value="3566" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>482</line>
          <option name="timeStamp" value="3609" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddItemFragment.java</url>
          <line>2868</line>
          <option name="timeStamp" value="3627" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1618</line>
          <option name="timeStamp" value="3656" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4949</line>
          <option name="timeStamp" value="3663" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>497</line>
          <option name="timeStamp" value="3664" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>968</line>
          <option name="timeStamp" value="3676" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>338</line>
          <option name="timeStamp" value="3679" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4862</line>
          <option name="timeStamp" value="3683" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4855</line>
          <option name="timeStamp" value="3684" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4811</line>
          <option name="timeStamp" value="3685" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>1251</line>
          <option name="timeStamp" value="3686" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>898</line>
          <option name="timeStamp" value="3690" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>713</line>
          <option name="timeStamp" value="3722" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/toobjects/StockItem.java</url>
          <line>136</line>
          <option name="timeStamp" value="3723" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddDocumentElementProcedure.java</url>
          <line>405</line>
          <option name="timeStamp" value="3727" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewInvoiceFragment.java</url>
          <line>656</line>
          <option name="timeStamp" value="3759" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewInvoiceFragment.java</url>
          <line>686</line>
          <option name="timeStamp" value="3760" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewInvoiceFragment.java</url>
          <line>714</line>
          <option name="timeStamp" value="3765" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/CorrectionInvoiceLineListFragment.java</url>
          <line>493</line>
          <option name="timeStamp" value="3778" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/CorrectionInvoiceLineListFragment.java</url>
          <line>520</line>
          <option name="timeStamp" value="3780" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/CorrectionInvoiceLineListFragment.java</url>
          <line>531</line>
          <option name="timeStamp" value="3781" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/edit/EditInvoiceProcedure.java</url>
          <line>66</line>
          <option name="timeStamp" value="3782" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/CorrectionInvoiceLineListFragment.java</url>
          <line>558</line>
          <option name="timeStamp" value="3784" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/CorrectionInvoiceLineListFragment.java</url>
          <line>583</line>
          <option name="timeStamp" value="3785" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/CorrectionInvoiceLineListFragment.java</url>
          <line>611</line>
          <option name="timeStamp" value="3786" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/CorrectionInvoiceLineListFragment.java</url>
          <line>632</line>
          <option name="timeStamp" value="3787" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/CorrectionInvoiceLineListFragment.java</url>
          <line>92</line>
          <option name="timeStamp" value="3790" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/CorrectionInvoiceLineListFragment.java</url>
          <line>519</line>
          <option name="timeStamp" value="3792" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewDocumentFragment.java</url>
          <line>1240</line>
          <option name="timeStamp" value="3794" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1075</line>
          <option name="timeStamp" value="3804" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4458</line>
          <option name="timeStamp" value="3811" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1606</line>
          <option name="timeStamp" value="3830" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1141</line>
          <option name="timeStamp" value="3854" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddRentalProcedure.java</url>
          <line>48</line>
          <option name="timeStamp" value="3883" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddRentalProcedure.java</url>
          <line>40</line>
          <option name="timeStamp" value="3884" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddRentalProcedure_OLD.java</url>
          <line>40</line>
          <option name="timeStamp" value="3885" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddRentalProcedure_OLD.java</url>
          <line>48</line>
          <option name="timeStamp" value="3886" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddRentalArticleProcedure.java</url>
          <line>41</line>
          <option name="timeStamp" value="3887" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddRentalArticleProcedure.java</url>
          <line>49</line>
          <option name="timeStamp" value="3888" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddRentalArticleProcedure.java</url>
          <line>70</line>
          <option name="timeStamp" value="3911" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>2492</line>
          <option name="timeStamp" value="3915" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/get/GetActualWarehouseItemsProcedure.java</url>
          <line>194</line>
          <option name="timeStamp" value="3920" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/get/GetItemByLocationProcedure.java</url>
          <line>48</line>
          <option name="timeStamp" value="3922" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/get/GetItemsLibrary.java</url>
          <line>86</line>
          <option name="timeStamp" value="3926" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>3740</line>
          <option name="timeStamp" value="3967" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>3748</line>
          <option name="timeStamp" value="3975" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/get/GetActualWarehouseItemsProcedure.java</url>
          <line>437</line>
          <option name="timeStamp" value="3987" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/WarehousePreviewFragment.java</url>
          <line>253</line>
          <option name="timeStamp" value="3995" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/WarehousePreviewFragment.java</url>
          <line>241</line>
          <option name="timeStamp" value="3996" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>964</line>
          <option name="timeStamp" value="4003" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewReturn_RentalArticlesFragment.java</url>
          <line>1989</line>
          <option name="timeStamp" value="4007" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/get/GetItemDeliveryProcedure.java</url>
          <line>147</line>
          <option name="timeStamp" value="4019" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/get/GetActualDocumentsProcedure.java</url>
          <line>492</line>
          <option name="timeStamp" value="4021" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>1325</line>
          <option name="timeStamp" value="4030" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1080</line>
          <option name="timeStamp" value="4038" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>409</line>
          <option name="timeStamp" value="4040" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>824</line>
          <option name="timeStamp" value="4043" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferElementFragment.java</url>
          <line>1324</line>
          <option name="timeStamp" value="4044" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>1613</line>
          <option name="timeStamp" value="4047" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementMultiLocationFragment.java</url>
          <line>1400</line>
          <option name="timeStamp" value="4049" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/utils/UnCaughtException.java</url>
          <line>140</line>
          <option name="timeStamp" value="4056" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/utils/UnCaughtException.java</url>
          <line>186</line>
          <option name="timeStamp" value="4057" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/utils/UnCaughtException.java</url>
          <line>171</line>
          <option name="timeStamp" value="4058" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>1319</line>
          <option name="timeStamp" value="4065" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>3565</line>
          <option name="timeStamp" value="4069" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>4458</line>
          <option name="timeStamp" value="4070" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewReturn_RentalArticlesFragment.java</url>
          <line>594</line>
          <option name="timeStamp" value="4072" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/utils/UnCaughtException.java</url>
          <line>199</line>
          <option name="timeStamp" value="4075" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/utils/UnCaughtException.java</url>
          <line>144</line>
          <option name="timeStamp" value="4076" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/utils/UnCaughtException.java</url>
          <line>135</line>
          <option name="timeStamp" value="4077" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/utils/UnCaughtException.java</url>
          <line>85</line>
          <option name="timeStamp" value="4078" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferSerialNrElementFragment.java</url>
          <line>565</line>
          <option name="timeStamp" value="4089" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferSerialNrElementFragment.java</url>
          <line>583</line>
          <option name="timeStamp" value="4092" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewDocumentFragment.java</url>
          <line>1610</line>
          <option name="timeStamp" value="4093" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/ChooseSerialNumbersFragment.java</url>
          <line>113</line>
          <option name="timeStamp" value="4098" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/ChooseSerialNumbersFragment.java</url>
          <line>350</line>
          <option name="timeStamp" value="4100" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewOrderArticlesFragment.java</url>
          <line>1383</line>
          <option name="timeStamp" value="4120" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewOrderArticlesFragment.java</url>
          <line>515</line>
          <option name="timeStamp" value="4130" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewOrderArticlesFragment.java</url>
          <line>485</line>
          <option name="timeStamp" value="4132" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewOrderArticlesFragment.java</url>
          <line>491</line>
          <option name="timeStamp" value="4135" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddSaveCorrectionElementProcedure.java</url>
          <line>29</line>
          <option name="timeStamp" value="4138" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddSaveCorrectionElementsProcedure.java</url>
          <line>220</line>
          <option name="timeStamp" value="4154" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddSaveCorrectionElementsProcedure.java</url>
          <line>168</line>
          <option name="timeStamp" value="4155" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddSaveCorrectionElementsProcedure.java</url>
          <line>110</line>
          <option name="timeStamp" value="4156" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddSaveCorrectionElementsProcedure.java</url>
          <line>186</line>
          <option name="timeStamp" value="4157" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddSaveCorrectionElementsProcedure.java</url>
          <line>83</line>
          <option name="timeStamp" value="4159" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/resultAsyncTasks/GetMatchingDocumentNrProcedureResultAsyncTask.java</url>
          <line>27</line>
          <option name="timeStamp" value="4177" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewOrderFragment.java</url>
          <line>869</line>
          <option name="timeStamp" value="4178" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/ApproveInventoryProcedure.java</url>
          <line>57</line>
          <option name="timeStamp" value="4191" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/ApproveInventoryProcedure.java</url>
          <line>44</line>
          <option name="timeStamp" value="4192" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/adapters/ReceiptsListAdapter.java</url>
          <line>116</line>
          <option name="timeStamp" value="4210" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddSaveInventoryElementProcedure.java</url>
          <line>88</line>
          <option name="timeStamp" value="4226" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddDocumentElementProcedure.java</url>
          <line>465</line>
          <option name="timeStamp" value="4227" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddSerialsToLocationChangeDocumentProcedure.java</url>
          <line>132</line>
          <option name="timeStamp" value="4233" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3521</line>
          <option name="timeStamp" value="4249" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/add/AddElementFromSerialNrProcedure.java</url>
          <line>84</line>
          <option name="timeStamp" value="4251" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewDocumentFragment.java</url>
          <line>1532</line>
          <option name="timeStamp" value="4257" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3822</line>
          <option name="timeStamp" value="4263" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3763</line>
          <option name="timeStamp" value="4264" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/utils/DialogUtils.java</url>
          <line>33</line>
          <option name="timeStamp" value="4266" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/AddItemFragmentListenerHandler.java</url>
          <line>85</line>
          <option name="timeStamp" value="4267" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/adapters/WarehouseItemsForDocumentAdapter.java</url>
          <line>247</line>
          <option name="timeStamp" value="4268" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/adapters/WarehouseItemsListAdapter.java</url>
          <line>498</line>
          <option name="timeStamp" value="4269" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4236</line>
          <option name="timeStamp" value="4270" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddItemFragment.java</url>
          <line>1797</line>
          <option name="timeStamp" value="4272" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>509</line>
          <option name="timeStamp" value="4274" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddInterbranchTransferSerialNrElementFragment.java</url>
          <line>326</line>
          <option name="timeStamp" value="4282" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/get/GetMatchingDocOrderNrProcedure.java</url>
          <line>52</line>
          <option name="timeStamp" value="4292" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/procedures/get/GetMatchingDocOrderNrProcedure.java</url>
          <line>58</line>
          <option name="timeStamp" value="4293" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/NewOrderFragment.java</url>
          <line>849</line>
          <option name="timeStamp" value="4318" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/MainActivity.java</url>
          <line>2074</line>
          <option name="timeStamp" value="4331" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3097</line>
          <option name="timeStamp" value="4334" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>3093</line>
          <option name="timeStamp" value="4335" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4841</line>
          <option name="timeStamp" value="4338" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/fragments/AddDocumentElementFragment.java</url>
          <line>4840</line>
          <option name="timeStamp" value="4339" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/app/src/main/java/com/longint/lomag/lomagweb/db/resultAsyncTasks/ProcedureExecutionAsyncTask.java</url>
          <line>69</line>
          <option name="timeStamp" value="4344" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="androidx.appcompat.widget.AppCompatEditText" memberName="mEditableFactory" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="AndroidRunConfigurationType">
        <watch expression="pattern_regex.charAt(i)" />
      </configuration>
    </watches-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/LomagWebAndroidStudio$LomagWebAndroidStudio.ic" NAME="LomagWebAndroidStudio Coverage Results" MODIFIED="1669535735548" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
  </component>
  <component name="play_dynamic_filters_status">
    <option name="appIdToCheckInfo">
      <map>
        <entry key="com.longint.lomag.lomagweb">
          <value>
            <CheckInfo lastCheckTimestamp="1751055565864" />
          </value>
        </entry>
        <entry key="com.longint.lomag.lomagweb.test">
          <value>
            <CheckInfo lastCheckTimestamp="1751055565862" />
          </value>
        </entry>
        <entry key="jim.h.common.android.lib.zxing.test">
          <value>
            <CheckInfo lastCheckTimestamp="1739945255939" />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>