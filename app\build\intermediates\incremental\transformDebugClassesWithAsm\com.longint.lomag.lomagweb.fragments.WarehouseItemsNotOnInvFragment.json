{"className": "com.longint.lomag.lomagweb.fragments.WarehouseItemsNotOnInvFragment", "classAnnotations": [], "interfaces": ["android.content.ComponentCallbacks", "android.view.View$OnCreateContextMenuListener", "androidx.lifecycle.LifecycleOwner", "androidx.lifecycle.ViewModelStoreOwner", "androidx.savedstate.SavedStateRegistryOwner", "com.longint.lomag.lomagweb.adapters.WarehouseItemsNotOnInventoryAdapter$WarehouseItemsNotOnInventoryListener"], "superClasses": ["androidx.fragment.app.Fragment", "java.lang.Object"]}