package com.longint.lomag.lomagweb.fragments;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.Bundle;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.longint.lomag.lomagweb.LomagApplication;
import com.longint.lomag.lomagweb.MainActivity;
import com.longint.lomag.lomagweb.R;
import com.longint.lomag.lomagweb.ScanningActivity;
import com.longint.lomag.lomagweb.data.SelectedWarehouseData;
import com.longint.lomag.lomagweb.db.toobjects.DocumentElement;
import com.longint.lomag.lomagweb.db.toobjects.DocumentType;
import com.longint.lomag.lomagweb.db.toobjects.SerialNumber;
import com.longint.lomag.lomagweb.utils.CodeFormatsGenerator;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import jim.h.common.android.lib.zxing.Intents;

public class ChooseSerialNumbersFragment extends Fragment {

    private MainActivity mainActivity;
    private Handler handler_serialNumberText = new Handler();

    public interface Listener {
        void onSerialNumbersChosen(List<SerialNumber> chosenSerialNumbers);
        void onSerialNumbersCancel();
    }

    public static ChooseSerialNumbersFragment newInstance(
            int numSerialNumbersToChoose, List<SerialNumber> serialNumbers, Listener listener) {
        ChooseSerialNumbersFragment fragment = new ChooseSerialNumbersFragment();
        fragment.listener = listener;
        fragment.numSerialNumbersToChoose = numSerialNumbersToChoose;
        fragment.serialNumbers = serialNumbers;
        return fragment;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View root = inflater.inflate(R.layout.fragment_choose_serial_numbers, null);

        remainingSerialNumbersLabel = (TextView)root.findViewById(R.id.remainingSerialNumbersLabel);
        SelectedWarehouseData.getInstance().setAdding(false);
        serialNumberText = (AutoCompleteTextView)root.findViewById(R.id.serialNumberText);
        serialNumberText.setAdapter(
                new ArrayAdapter<SerialNumber>(
                        getActivity(),
                        android.R.layout.simple_list_item_1,
                        serialNumbers));
        serialNumberText.setThreshold(1);
        serialNumberText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                hideErrorsLabels();

                if (s.length() > 0)
                {
                    if (s.toString().indexOf("\n") > -1 || s.toString().indexOf("\r") > -1)
                    {
                        String sn = s.toString().trim();
                        serialNumberText.setText(sn);
                        addSerialNumberButtonFunction(sn);
                    }
                }

            }
        });

        chooseSerialNumberButton = (ImageView)root.findViewById(R.id.chooseSerialNumberButton);
        chooseSerialNumberButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                hideErrorsLabels();
                ChooseOneSerialNumberFragment fragment = ChooseOneSerialNumberFragment.newInstance(
                        serialNumbers,
                        new ChooseOneSerialNumberFragment.Listener() {
                            @Override
                            public void onSerialNumberChosen(SerialNumber chosenSerialNumber) {
                                mainActivity.popBackStack();
                                if (!serialNumbers.contains(chosenSerialNumber)) {
                                    serialNumberDoesNotExistError.setVisibility(View.VISIBLE);
                                    return;
                                }
                                if (chosenSerialNumbers.contains(chosenSerialNumber)) {
                                    serialNumberIsOnListError.setText(
                                            getString(R.string.serial_in_use, chosenSerialNumber.toString()));
                                    serialNumberIsOnListError.setVisibility(View.VISIBLE);
                                    return;
                                }
                                chosenSerialNumbers.add(chosenSerialNumber);
                                updateUserInterface();
                            }
                        });

                FragmentManager fm = getActivity().getSupportFragmentManager();
                FragmentTransaction ft = fm.beginTransaction();

                ft.add(R.id.container, fragment, ChooseOneSerialNumberFragment.class.getName());
                ft.addToBackStack(ChooseOneSerialNumberFragment.class.getName());
                ft.commit();
            }
        });

        scanSerialNumberButton = (ImageView)root.findViewById(R.id.scanSerialNumberButton);
        scanSerialNumberButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                hideErrorsLabels();
                startScanning(ScanningActivity.SERIAL_CODE);
            }
        });

        serialNumberDoesNotExistError = (TextView)root.findViewById(R.id.serialNumberDoesNotExistError);

        serialNumberIsOnListError = (TextView)root.findViewById(R.id.serialNumberIsOnListError);

        addSerialNumberButton = (LinearLayout)root.findViewById(R.id.addSerialNumberButton);
        addSerialNumberButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addSerialNumberButtonFunction(serialNumberText.getText().toString().trim());
            }
        });

        automaticallyChooseSerialNumbersButton
                = (LinearLayout)root.findViewById(R.id.automaticallyChooseSerialNumbersButton);
        automaticallyChooseSerialNumbersButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                hideErrorsLabels();

                int documentId = SelectedWarehouseData.getInstance().getCurrentDocType().getId();
                int mode = SelectedWarehouseData.getInstance().getModeWzPicking();
                if (documentId == DocumentType.INV_ID || documentId == DocumentType.REGISTER_SHEET_ID ||
                        ((documentId == DocumentType.RELEASE_ID && mode == SelectedWarehouseData.MODE_WZ_PICKING) ||
                                (documentId == DocumentType.RENTAL_ID && mode == SelectedWarehouseData.MODE_WY_PICKING))) {
                    for (int i = 0; i < serialNumbers.size() && i < numSerialNumbersToChoose; ++i) {
                        chosenSerialNumbers.add(serialNumbers.get(i));
                    }
                }
                else {
                    List<DocumentElement> list = SelectedWarehouseData.getInstance().getIncludedDeliveries();
                    if (list == null) {
                        for (int i = 0; i < serialNumbers.size() && i < numSerialNumbersToChoose; ++i) {
                            chosenSerialNumbers.add(serialNumbers.get(i));
                        }
                    } else {
                        HashMap<Integer, Integer> docSn = new HashMap<>();
                        for (int y = 0; y < list.size(); ++y) {
                            DocumentElement de = list.get(y);
                            if (de != null) {
                                docSn.put(de.getId(), 0);
                            }
                        }

                        int numSerialNumbersChoose = 0;
                        for (int i = 0; i < serialNumbers.size(); ++i) {
                            SerialNumber sn = serialNumbers.get(i);
                            if (sn != null) {
                                for (int y = 0; y < list.size(); ++y) {
                                    DocumentElement de = list.get(y);
                                    if (de != null) {
                                        Integer q = docSn.get(de.getId());
                                        if (de.getId() == sn.getDocElementId() && q < de.getQuantityEdit()) {
                                            docSn.put(de.getId(), q + 1);
                                            chosenSerialNumbers.add(sn);
                                            numSerialNumbersChoose++;
                                        }
                                    }

                                    if (numSerialNumbersToChoose <= numSerialNumbersChoose) {
                                        break;
                                    }
                                }
                            }

                            if (numSerialNumbersToChoose <= numSerialNumbersChoose) {
                                break;
                            }
                        }
                    }
                }

                updateUserInterface();
            }
        });

        serialNumbersContainer = (LinearLayout)root.findViewById(R.id.serialNumbersContainer);

        saveSerialNumbersButton = (LinearLayout)root.findViewById(R.id.saveSerialNumbersButton);
        saveSerialNumbersButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                listener.onSerialNumbersChosen(chosenSerialNumbers);
            }
        });

        cancelSerialNumbersButton = (LinearLayout)root.findViewById(R.id.cancelSerialNumbersButton);
        cancelSerialNumbersButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                listener.onSerialNumbersCancel();
                //mainActivity.popBackStack();
            }
        });

        hideErrorsLabels();
        updateUserInterface();

        set_focus_serialNumberText();

        return root;
    }

    private void set_focus_serialNumberText()
    {

        try
        {
            handler_serialNumberText.removeCallbacks(runnable_serialNumberText);
        }
        catch (Exception e)
        {
            Log.e(LomagApplication.APP_TAG, "AddDocumentElementFragment removeCallbacks: " + e.getMessage());
        }

        try
        {
            handler_serialNumberText.postDelayed(runnable_serialNumberText, 1000);
        }
        catch (Exception e)
        {
            Log.e(LomagApplication.APP_TAG, "AddDocumentElementFragment postDelayed: " + e.getMessage());
        }

    }

    public void addSerialNumberButtonFunction(String snText) {

        hideErrorsLabels();
        SerialNumber number = new SerialNumber();
        number.setSerialNr(snText);

        if (!TextUtils.isEmpty(snText)) {
            for (SerialNumber serial : serialNumbers) {
                if (snText.trim().equalsIgnoreCase(serial.getSerialNr().trim()))
                {
                    number.setDocElementId(serial.getDocElementId());
                    number.setId(serial.getId());
                    break;
                }
            }
        }

        int documentId = SelectedWarehouseData.getInstance().getCurrentDocType().getId();
        if (documentId != DocumentType.REGISTER_SHEET_ID && documentId != DocumentType.INV_ID) {
            if (!serialNumbers.contains(number)) {
                serialNumberDoesNotExistError.setVisibility(View.VISIBLE);
                return;
            }
        }
        if (chosenSerialNumbers.contains(number)) {
            serialNumberIsOnListError.setText(getString(R.string.serial_in_use, number.toString()));
            serialNumberIsOnListError.setVisibility(View.VISIBLE);
            return;
        }
        chosenSerialNumbers.add(number);
        updateUserInterface();

        serialNumberText.setText("");
        set_focus_serialNumberText();
    }


    @Override
    public void onAttach(Context context) {
        super.onAttach(context);

        try {

            mainActivity = (MainActivity) context;
        } catch (ClassCastException e) {
            throw new ClassCastException(context.toString() + " must implement ChooseSerialNumbersFragment");
        }
    }
    @Override
    public void onAttach(Activity context) {
        super.onAttach(context);

        try {
            mainActivity = (MainActivity) context;
        } catch (ClassCastException e) {
            throw new ClassCastException(context.toString() + " must implement ChooseSerialNumbersFragment");
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == ScanningActivity.SERIAL_CODE) {
                SerialNumber number = new SerialNumber();
                number.setSerialNr(data.getStringExtra(ScanningActivity.BARCODE_TAG));

                if (!TextUtils.isEmpty(data.getStringExtra(ScanningActivity.BARCODE_TAG))) {
                    for (SerialNumber serial : serialNumbers) {
                        if (data.getStringExtra(ScanningActivity.BARCODE_TAG).trim().equalsIgnoreCase(serial.getSerialNr().trim()))
                        {
                            number.setDocElementId(serial.getDocElementId());
                            number.setId(serial.getId());
                            break;
                        }
                    }
                }

                int documentId = SelectedWarehouseData.getInstance().getCurrentDocType().getId();
                if (documentId != DocumentType.REGISTER_SHEET_ID && documentId != DocumentType.INV_ID) {
                    if (!serialNumbers.contains(number)) {
                        serialNumberDoesNotExistError.setVisibility(View.VISIBLE);
                        return;
                    }
                }
                if (chosenSerialNumbers.contains(number)) {
                    serialNumberIsOnListError.setText(getString(R.string.serial_in_use, number.toString()));
                    serialNumberIsOnListError.setVisibility(View.VISIBLE);
                    return;
                }
                chosenSerialNumbers.add(number);
                updateUserInterface();
            }
        }
    }

    private void hideErrorsLabels() {
        serialNumberDoesNotExistError.setVisibility(View.GONE);
        serialNumberIsOnListError.setVisibility(View.GONE);
    }

    private void updateUserInterface() {
        int missingSerialNumbers = numSerialNumbersToChoose - chosenSerialNumbers.size();
        remainingSerialNumbersLabel.setText(getString(R.string.remaining_serials, missingSerialNumbers));
        if (missingSerialNumbers == 0) {
            remainingSerialNumbersLabel.setTextColor(Color.BLACK);
            disableButton(chooseSerialNumberButton);
            disableButton(scanSerialNumberButton);
            disableButton(addSerialNumberButton);
            disableButton(automaticallyChooseSerialNumbersButton);
            enableButton(saveSerialNumbersButton);
        }
        else {
            remainingSerialNumbersLabel.setTextColor(Color.RED);
            enableButton(chooseSerialNumberButton);
            enableButton(scanSerialNumberButton);
            enableButton(addSerialNumberButton);
            if (chosenSerialNumbers.size() > 0) {
                disableButton(automaticallyChooseSerialNumbersButton);
            }
            else {
                enableButton(automaticallyChooseSerialNumbersButton);
            }
            disableButton(saveSerialNumbersButton);
        }

        updateSerialNumbersContainer();
    }

    private void updateSerialNumbersContainer() {
        serialNumbersContainer.removeAllViews();
        for (final SerialNumber number : chosenSerialNumbers) {
            View item = View.inflate(getActivity(), R.layout.choose_serial_numbers_fragment_remove_item, null);

            TextView serialNumberText = (TextView)item.findViewById(R.id.serialNumberText);
            serialNumberText.setText(number.toString());

            ImageView removeSerialNumberButton = (ImageView)item.findViewById(R.id.removeSerialNumberButton);
            removeSerialNumberButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    chosenSerialNumbers.remove(number);
                    updateUserInterface();
                }
            });

            // Add dedicated columns button functionality
            ImageView dedicatedColumnsButton = (ImageView)item.findViewById(R.id.dedicatedColumnsButton);

            // Show dedicated columns button only for MM documents
            DocumentType currentDocType = SelectedWarehouseData.getInstance().getCurrentDocType();
            if (currentDocType != null && DocumentType.INTERBRANCH_TRANSFER_SHORT_NAME.equals(currentDocType.getShortName())) {
                dedicatedColumnsButton.setVisibility(View.VISIBLE);
                dedicatedColumnsButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        // Use MainActivity's method to show dedicated columns
                        if (mainActivity instanceof AddSerialNumbersFragment.AddSerialFragmentListener) {
                            ((AddSerialNumbersFragment.AddSerialFragmentListener) mainActivity)
                                    .onSerialNumberDedicatedColumnsClicked(number);
                        }
                    }
                });
            } else {
                dedicatedColumnsButton.setVisibility(View.GONE);
            }

            serialNumbersContainer.addView(item);
        }
    }

    private void disableButton(LinearLayout button) {
        button.setEnabled(false);
        for (int childId = 0; childId < button.getChildCount(); ++childId) {
            button.getChildAt(childId).setEnabled(false);
        }
    }

    private void enableButton(LinearLayout button) {
        button.setEnabled(true);
        for (int childId = 0; childId < button.getChildCount(); ++childId) {
            button.getChildAt(childId).setEnabled(true);
        }
    }

    private void disableButton(ImageView button) {
        button.setEnabled(false);
    }

    private void enableButton(ImageView button) {
        button.setEnabled(true);
    }

    private void startScanning(int code) {
        if (ContextCompat.checkSelfPermission(
                    getActivity(),
                    Manifest.permission.CAMERA)
                != PackageManager.PERMISSION_GRANTED) {
            SelectedWarehouseData.getInstance().setCode(code);
            ActivityCompat.requestPermissions(
                    getActivity(),
                    new String[]{ Manifest.permission.CAMERA },
                    MainActivity.MY_PERMISSIONS_REQUEST_CAMERA);
        }
        else{
            Intent intent = new Intent(getActivity(), ScanningActivity.class);
            intent.putExtra(
                    Intents.Scan.FORMATS,
                    CodeFormatsGenerator.generateScanFormats(getActivity()));
            startActivityForResult(intent, code);
        }
    }

    private Runnable runnable_serialNumberText = new Runnable() {
        @Override
        public void run() {

            FragmentActivity f = getActivity();
            if (f != null) {
                f.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {

                        try {

                            if (serialNumberText != null) {

                                serialNumberText.requestFocus();
                                serialNumberText.clearFocus();
                                serialNumberText.requestFocus();
                                serialNumberText.setSelection(serialNumberText.getText().length(), 0);

                            }

                        } catch (Exception e) {
                            Log.e(LomagApplication.APP_TAG, "runnable_quantity: " + e.toString());
                        }

                    }
                });
            }
        }
    };

    private Listener listener;
    private int numSerialNumbersToChoose;
    private List<SerialNumber> serialNumbers;
    private List<SerialNumber> chosenSerialNumbers = new ArrayList<SerialNumber>();
    private TextView remainingSerialNumbersLabel;
    private AutoCompleteTextView serialNumberText;
    private ImageView chooseSerialNumberButton;
    private ImageView scanSerialNumberButton;
    private TextView serialNumberDoesNotExistError;
    private TextView serialNumberIsOnListError;
    private LinearLayout addSerialNumberButton;
    private LinearLayout automaticallyChooseSerialNumbersButton;
    private LinearLayout serialNumbersContainer;
    private LinearLayout saveSerialNumbersButton;
    private LinearLayout cancelSerialNumbersButton;


}
