package com.longint.lomag.lomagweb.db.procedures.add;

import android.content.Context;
import android.util.Log;


import com.longint.lomag.lomagweb.LomagApplication;
import com.longint.lomag.lomagweb.data.AndroidUserData;
import com.longint.lomag.lomagweb.data.SelectedWarehouseData;
import com.longint.lomag.lomagweb.db.procedures.Procedure;
import com.longint.lomag.lomagweb.db.toobjects.DocumentElement;
import com.longint.lomag.lomagweb.db.toobjects.SerialNumber;
import com.longint.lomag.lomagweb.db.procedures.add.SaveSerialNumberDedicatedColumnsProcedure;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collection;

public class AddSaveInventoryElementProcedure implements Procedure {
	private enum ExecutionResult {
		UNKNOWN,
		OK,
		AMOUNT_IS_TOO_LARGE,
		EXCEPTION_OCCURRED
	}

	private ExecutionResult executionResult;
	private PreparedStatement _statement;
	private DocumentElement docElement;
	private PreparedStatement _getIDStatement;
	private PreparedStatement _serialStatement;
	private boolean finishAfter;
	private Context context;
	private Collection<SerialNumber> serialNrs;
	private static final String INSERT_SERIAL_NR_STATEMENT = "INSERT INTO "+SerialNumber.TABLE_NAME + " VALUES (?,?)";
	private final String GET_INSERTED_ELEMENT_ID_STATEMENT = "SELECT TOP 1 "+DocumentElement.ID_NAME+" FROM "+DocumentElement.TABLE_NAME +
			" WHERE "+DocumentElement.USER_NAME+ " = ?"+ " ORDER BY "+ DocumentElement.ID_NAME+" DESC";

	public AddSaveInventoryElementProcedure(DocumentElement documentElement, Collection<SerialNumber> serialNrs, boolean finishAfter, Context context) {
		executionResult = ExecutionResult.UNKNOWN;
		docElement = documentElement;
		this.serialNrs = serialNrs;
		this.finishAfter = finishAfter;
		this.context = context;
	}

	public DocumentElement getDocElement()
	{
		return docElement;
	}

	@Override
	public ResultSet executeProcedure(Connection connection) throws SQLException {
		try {
			_statement = connection.prepareStatement("EXEC dbo.SaveInventoryElement ?, ?, ?, ?, ?, ?, ?, ?, ?, ?");
			_statement.setInt(1, docElement.getId());
			_statement.setInt(2, docElement.getDocument().getId());
			_statement.setInt(3, docElement.getItem().getId());
			_statement.setDouble(4, docElement.getCount());
			_statement.setDouble(5, docElement.getUnitPrice());
			_statement.setString(6, (docElement.getRemarks() == null) ? "" : docElement.getRemarks());
			_statement.setString(7, (docElement.getSeriesNr() == null) ? "" : docElement.getSeriesNr());
			_statement.setDate(8, (docElement.getValidationDate() == null) ? null : new java.sql.Date(docElement.getValidationDate().getTime()));
			_statement.setString(9, null);
			_statement.setInt(10, docElement.getUser().getId());
			Log.i(LomagApplication.APP_TAG, "statement is opened:" + !_statement.isClosed());
			_statement.executeUpdate();

			if (serialNrs != null)
			{
				_getIDStatement = connection.prepareStatement(GET_INSERTED_ELEMENT_ID_STATEMENT);
				_getIDStatement.setInt(1, AndroidUserData.getInstance().getUser(context));
				ResultSet rs = _getIDStatement.executeQuery();
				int id = 0;
				if (rs.next()) {
					id = rs.getInt(DocumentElement.ID_NAME);
				}
				for (SerialNumber serial : serialNrs) {
					_serialStatement = connection.prepareStatement(INSERT_SERIAL_NR_STATEMENT);
					_serialStatement.setInt(1, id);
					_serialStatement.setString(2, serial.getSerialNr());
					_serialStatement.executeUpdate();

					// Insert dedicated columns data if available - use SaveSerialNumberDedicatedColumnsProcedure
					if (serial.getDedicatedColumnsData() != null && !serial.getDedicatedColumnsData().isEmpty()) {
						// Use the existing SaveSerialNumberDedicatedColumnsProcedure
						SaveSerialNumberDedicatedColumnsProcedure dcProcedure = new SaveSerialNumberDedicatedColumnsProcedure(docElement.getItem().getId(), serial);
						dcProcedure.executeProcedure(connection);
					}
				}
			}

			DocumentElement element = new DocumentElement(docElement);
			SelectedWarehouseData.getInstance().setAddListDocElemet(element);

			executionResult = ExecutionResult.OK;
		}
		catch (SQLException e) {
			if (e.getMessage().equals(docElement.getItem().getId() + "")) {
				executionResult = ExecutionResult.AMOUNT_IS_TOO_LARGE;
			}
			else {
				executionResult = ExecutionResult.EXCEPTION_OCCURRED;
				throw e;
			}
		}

		return null;
	}

	public boolean isAmountTooLarge() {
		return executionResult == ExecutionResult.AMOUNT_IS_TOO_LARGE;
	}

	public boolean getFinishAfter() {
		return finishAfter;
	}

	public String getWrongItemId() {
		return docElement.getItem().getId() + "";
	}

	public String getWrongItemName() {
		return docElement.getItem().getName();
	}

	public String getWrongItemBarcode() {
		return docElement.getItem().getBarcode();
	}
}
