package com.longint.lomag.lomagweb.fragments;

import android.app.Activity;
import androidx.fragment.app.Fragment;
import android.content.Context;
import android.os.AsyncTask;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.longint.lomag.lomagweb.LomagApplication;
import com.longint.lomag.lomagweb.MainActivity;

import com.longint.lomag.lomagweb.R;
import com.longint.lomag.lomagweb.adapters.SerialNrListAdapter;
import com.longint.lomag.lomagweb.data.SelectedWarehouseData;
import com.longint.lomag.lomagweb.db.procedures.get.GetDedicatedColumnProcedure;
import com.longint.lomag.lomagweb.db.procedures.get.GetItemDeliveryProcedure;
import com.longint.lomag.lomagweb.db.procedures.get.GetSerialsForDeliverysProcedure;
import com.longint.lomag.lomagweb.db.procedures.Procedure;
import com.longint.lomag.lomagweb.db.resultAsyncTasks.ProcedureExecutionAsyncTask;
import com.longint.lomag.lomagweb.db.toobjects.DedicatedColumn;
import com.longint.lomag.lomagweb.db.toobjects.DocumentElement;
import com.longint.lomag.lomagweb.db.toobjects.DocumentType;
import com.longint.lomag.lomagweb.db.toobjects.Location;
import com.longint.lomag.lomagweb.db.toobjects.SerialNumber;
import com.longint.lomag.lomagweb.db.toobjects.SettingsObject;
import com.longint.lomag.lomagweb.db.toobjects.StockItem;
import com.longint.lomag.lomagweb.utils.DedicatedColumnsValidator;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;

@SuppressWarnings("deprecation")
public class AddSerialNumbersFragment extends Fragment implements SerialNrListAdapter.OnSerialNrChangedListener, ProcedureExecutionAsyncTask.ProcedureExecutionListener {

	private TextView textViewRemainingSerials;
    private ImageView imageViewScan;
    private EditText editTextAddSerial;
    private RelativeLayout relativeLayoutAddButton;
    private RelativeLayout relativeLayoutOkButton;
    private RelativeLayout relativeLayoutCancelButton;
    private RelativeLayout relativeLayoutSkipButton;
	private RelativeLayout relativeLayoutAddAuto;
	private TextView textViewAddButton;
	private TextView textViewOkButton;
	private TextView textViewCancelButton;
	private TextView textViewSkipButton;
	private TextView textViewAddAuto;
	private AutoCompleteTextView autoCompleteTextViewSerial;
    private ListView listViewSerialNumbers;
	private TextView textViewNoSerialError;
	private ImageView imageSearchForSerial;
	private int how_much_required;
	private int quant_diff;
	private boolean finishAfter;
	private SerialNrListAdapter adapter;
	private ArrayAdapter<SerialNumber> serialRemoveAdapter;
	private String str_doc_ids="",chosenSerial="";
	public static String TAG_HOW_MUCH_REQUIRED = "how_much";
	public static String TAG_FINISH_AFTER = "finish_after";
	public static String TAG_DOC_IDS = "doc_ids";

	private boolean allSerial = false;
	private MainActivity mainActivity;

	private void setEnabled(boolean enabled, RelativeLayout layout, TextView textView)
	{
		layout.setEnabled(enabled);
		if (enabled)
		{
			//layout.setBackgroundColor(getResources().getColor(R.color.main_background_color));
			textView.setTextColor(getResources().getColor(R.color.black));
		}
		else{

		//	layout.setBackgroundColor(getResources().getColor(R.color.disable_backgroung_color));
			textView.setTextColor(getResources().getColor(R.color.disable_text_color));

		}
	}

	@Override
	public void onSerialNrChanged(int newNr)
	{
		if (how_much_required-newNr == 0)
		{
			setEnabled(true,relativeLayoutOkButton,textViewOkButton);
			setEnabled(false,relativeLayoutAddButton,textViewAddButton);
			imageViewScan.setEnabled(false);
			textViewRemainingSerials.setVisibility(View.GONE);
			allSerial =true;
		}else{
			allSerial = false;
			setEnabled(false,relativeLayoutOkButton,textViewOkButton);
			setEnabled(true,relativeLayoutAddButton,textViewAddButton);
			imageViewScan.setEnabled(true);
			textViewRemainingSerials.setVisibility(View.VISIBLE);
			textViewRemainingSerials.setText(String.format(Locale.US,getString(R.string.remaining_serials),how_much_required-newNr));
		}
	}

	@Override
	public void onDedicatedColumnsClicked(SerialNumber serialNumber) {
		mListener.onSerialNumberDedicatedColumnsClicked(serialNumber);
	}
	public boolean isFinishAfter() {
		return finishAfter;
	}

	private boolean isAlreadyAdded(SerialNumber serialNumber)
	{
		for (SerialNumber serial: SelectedWarehouseData.getInstance().getSerialNumbersList())
		{
			if (serial.getSerialNr().trim().toLowerCase().equals(serialNumber.getSerialNr().trim().toLowerCase()))
				return true;
		}
		return false;
	}

	@Override
	public void onProcedureExecuted(Object result, Procedure procedure) {
		try {
			if (procedure instanceof GetItemDeliveryProcedure)
			{
				onGetItemDeliveryProcedureExecuted((GetItemDeliveryProcedure) procedure);
			}
			else if(procedure instanceof GetSerialsForDeliverysProcedure)
			{
				onGetSerialsForDeliverysProcedureExecuted((Collection<SerialNumber>) result);
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}
	private void startGetDeliveryItemProcedure(StockItem item, Date maxDate)
	{
		GetItemDeliveryProcedure procedure = new GetItemDeliveryProcedure(item, null, false, maxDate,getActivity());
		executeProcedure(procedure);
	}
	private void startGetSerialsForDeliveryProcedure(Collection<DocumentElement> deliverys)
	{
		GetSerialsForDeliverysProcedure procedure = new GetSerialsForDeliverysProcedure(deliverys);
		executeProcedure(procedure);
	}
	private void setVisibility()
	{
		textViewNoSerialError.setVisibility(View.GONE);
		if(isModeAddSerial())
		{
			autoCompleteTextViewSerial.setVisibility(View.GONE);
			relativeLayoutAddAuto.setVisibility(View.GONE);
			editTextAddSerial.setVisibility(View.VISIBLE);
			imageSearchForSerial.setVisibility(View.GONE);
		}
		else
		{
			autoCompleteTextViewSerial.setVisibility(View.VISIBLE);
			relativeLayoutAddAuto.setVisibility(View.VISIBLE);
			editTextAddSerial.setVisibility(View.INVISIBLE);
			imageSearchForSerial.setVisibility(View.VISIBLE);
		}
	}

	private void setLocationForDeliveryItem(DocumentElement documentElement, ResultSet resultSet) throws SQLException
	{
		String locationName = resultSet.getString("Nazwa lokalizacji");
		String locationCode = resultSet.getString("Kod lokalizacji");
		if ((locationCode != null && !locationCode.isEmpty()) || (locationName != null && !locationName.isEmpty())) {
			Location loc = new Location();
			loc.setName(locationName);
			loc.setCode(locationCode);
			documentElement.setLocation(loc);
		}
	}

	private void onGetItemDeliveryProcedureExecuted(GetItemDeliveryProcedure procedure) throws SQLException {

		int count = 0;
		LinkedList<DocumentElement> includedDeliverys = new LinkedList<DocumentElement>();

		if(TextUtils.isEmpty(str_doc_ids))
		{
			List<DocumentElement> deliveryItems = procedure.getDeliveryItems();
			Log.e(LomagApplication.APP_TAG,"onGetItemDeliveryProcedureExecuted deliveryItems- "+deliveryItems.size());

			Location curDocElemLoc = SelectedWarehouseData.getInstance().getCurrentDocElement().getLocation();

			int documentId = SelectedWarehouseData.getInstance().getCurrentDocType().getId();

			for (int i = deliveryItems.size() - 1; i > -1; --i)
			{
				DocumentElement documentElement = deliveryItems.get(i);
				Location docElemLoc = documentElement.getLocation();

				if (curDocElemLoc != null)
				{
					if (docElemLoc != null && curDocElemLoc.getCode().equals(docElemLoc.getCode()))
					{
						includedDeliverys.add(documentElement);
						count += documentElement.getCount();
					}
				}
				else
				{
					includedDeliverys.add(documentElement);
					count += documentElement.getCount();
				}

				if (count >= how_much_required)
				{
					break;
				}
			}

		}
		else
		{
			String[] doc_ids=str_doc_ids.split(",");

			for (int i=0; i<doc_ids.length; i++)
			{
				DocumentElement documentElement =new DocumentElement();
				documentElement.setId(Integer.parseInt(doc_ids[i]));
				includedDeliverys.add(documentElement);
			}
		}

		Log.e(LomagApplication.APP_TAG,"onGetItemDeliveryProcedureExecuted includedDeliverys - "+includedDeliverys.size());
		startGetSerialsForDeliveryProcedure(includedDeliverys);

	}

	private void onGetSerialsForDeliverysProcedureExecuted(Collection<SerialNumber> serials) throws SQLException {
		Log.e(LomagApplication.APP_TAG,"onGetSerialsForDeliverysProcedureExecuted - onGetSerialsForDeliverysProcedureExecuted ");
		if (getActivity() != null)
		{
			SelectedWarehouseData.getInstance().setAvaliableSerialNrList(serials);
			imageSearchForSerial.setEnabled(true);
			setEnabled(true,relativeLayoutAddAuto,textViewAddAuto);

			serialRemoveAdapter = new ArrayAdapter<SerialNumber>(getActivity(), android.R.layout.simple_list_item_1, new ArrayList<SerialNumber>(serials));
			autoCompleteTextViewSerial.setAdapter(serialRemoveAdapter);
			autoCompleteTextViewSerial.setThreshold(1);
		}
	}


	@Override
	public void onConnectionFailed(Procedure procedure, ProcedureExecutionAsyncTask.ProcedureExecutionListener listener, Exception e) {
		mListener.onSerialNumberCheckFailed(procedure, listener, e);
	}

	public interface AddSerialFragmentListener {

		void onSerialFragmentCreated(AddSerialNumbersFragment fragment);

		void onSerialScanButtonClicked();

		void onSerialNrOkClicked(Collection<SerialNumber> serialNrs, DocumentElement documentElement, boolean finishAfter);

		void onSerialNrCancelClicked(DocumentElement documentElement, boolean finishAfter);

		void showSerialDialogAlreadyExists(SerialNumber serial);

		void onSerialNumberCheckFailed(Procedure procedure, ProcedureExecutionAsyncTask.ProcedureExecutionListener listener, Exception e);

		void onSearchSerialButtonClicked();

		void onSerialNumberDedicatedColumnsClicked(SerialNumber serialNumber);
	}
    AddSerialFragmentListener mListener;

	private void setActionBar()
	{
		((AppCompatActivity) getActivity()).getSupportActionBar().setDisplayHomeAsUpEnabled(true);
		((AppCompatActivity) getActivity()).getSupportActionBar().setDisplayShowHomeEnabled(true);
		((AppCompatActivity) getActivity()).getSupportActionBar().setTitle(R.string.serial_nrs);
		((MainActivity)getActivity()).setMenuVisibility(false);
		((MainActivity)getActivity()).setRefreashVisibility(false);
		getActivity().invalidateOptionsMenu();
	}

	@Override
	public void onResume() {
		setActionBar();
		autoCompleteTextViewSerial.setText(chosenSerial);
		chosenSerial = "";
		super.onResume();
	}

	public void init(Bundle arg)
	{
		str_doc_ids= arg.getString(TAG_DOC_IDS);
		how_much_required = arg.getInt(TAG_HOW_MUCH_REQUIRED);
		if (isInv())
		{
			quant_diff = (int)SelectedWarehouseData.getInstance().getItemsFromIds().get(SelectedWarehouseData.getInstance().getCurrentDocElement().getItem().getId()).getInitialState()-how_much_required;
			if (quant_diff<0)
				how_much_required = Math.abs(quant_diff);
		}

		finishAfter = arg.getBoolean(TAG_FINISH_AFTER);
		textViewRemainingSerials.setText(String.format(Locale.US,getString(R.string.remaining_serials),how_much_required));
		imageSearchForSerial.setEnabled(false);
		setEnabled(false,relativeLayoutAddAuto,textViewAddAuto);

		/*
		if (isModeAddSerial() && SelectedWarehouseData.getInstance().getCurrentDocElement().getItem().isSerialNumber())
		{
			relativeLayoutSkipButton.setVisibility(View.VISIBLE);
		}
		else
		{
			relativeLayoutSkipButton.setVisibility(View.GONE);
		}*/
		relativeLayoutSkipButton.setVisibility(View.GONE);
	}

	private void addEditTextChangeListener()
	{
		editTextAddSerial.addTextChangedListener(new TextWatcher() {
			@Override
			public void beforeTextChanged(CharSequence s, int start, int count, int after) {

			}

			@Override
			public void onTextChanged(CharSequence s, int start, int before, int count) {

			}

			@Override
			public void afterTextChanged(Editable s) {
				if (s.length()==0)
					setEnabled(false,relativeLayoutAddButton,textViewAddButton);
				else if (!allSerial)
					setEnabled(true,relativeLayoutAddButton,textViewAddButton);

				if (s.length() > 0)
				{
					if (s.toString().indexOf("\n") > -1 || s.toString().indexOf("\r") > -1)
					{
						//textViewAddButton.performLongClick();
						if (isModeAddSerial())
						{
							addSerial(s.toString().trim());
						}
						else
						{
							addSerial(autoCompleteTextViewSerial.getText().toString());
						}
					}
				}
			}
		});
	}

	private void addAutoTextViewChangeListener()
	{
		autoCompleteTextViewSerial.addTextChangedListener(new TextWatcher() {
			@Override
			public void beforeTextChanged(CharSequence s, int start, int count, int after) {

			}

			@Override
			public void onTextChanged(CharSequence s, int start, int before, int count) {

			}

			@Override
			public void afterTextChanged(Editable s)
			{
				if (s.length()==0)
				{
					setEnabled(false,relativeLayoutAddButton,textViewAddButton);
				}
				else if (!allSerial)
				{
					setEnabled(true,relativeLayoutAddButton,textViewAddButton);
					Collection<SerialNumber> avaliableSerials = SelectedWarehouseData.getInstance().getAvaliableSerialNrList();
					boolean isOk = false;

					if (avaliableSerials != null) {
						for (SerialNumber serial : avaliableSerials) {
							if (serial.getSerialNr().trim().toLowerCase().equals(s.toString().trim().toLowerCase())) {
								isOk = true;
								textViewNoSerialError.setVisibility(View.GONE);
								break;
							}
						}
					}
					if (!isOk)
					{
						textViewNoSerialError.setVisibility(View.VISIBLE);
						setEnabled(false,relativeLayoutAddButton,textViewAddButton);
					}
				}
			}
		});
	}

	private boolean isIssue() {
		int docId = SelectedWarehouseData.getInstance().getCurrentDocType().getId();
		return docId == DocumentType.RELEASE_ID || docId == DocumentType.INTERNAL_GOODS_RW_ID ||  docId == DocumentType.RENTAL_ID ||  docId == DocumentType.RETURN_FROM_RENTAL_ID ;
	}

	private boolean isRecepit() {
		int docId = SelectedWarehouseData.getInstance().getCurrentDocType().getId();
		return docId == DocumentType.RECEIVE_ID || docId == DocumentType.INTERNAL_GOODS_PW_ID;
	}

	private boolean isInv()
	{
		return SelectedWarehouseData.getInstance().getCurrentDocType().getId()==DocumentType.INV_ID;
	}

	private void executeProcedure(Procedure procedure)
	{
		ProcedureExecutionAsyncTask procedureAsyncTask = new ProcedureExecutionAsyncTask(procedure,this);
		procedureAsyncTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
	}

	private void addSerial(SerialNumber serial){
		adapter.addSerial(serial);
		SelectedWarehouseData.getInstance().getSerialNumbersList().add(serial);
		editTextAddSerial.setText("");
		autoCompleteTextViewSerial.setText("");
	}

	private boolean isSerialOk(SerialNumber serial, StockItem item)
	{
		if (SelectedWarehouseData.getInstance().getSerialSupportMode().equals(SettingsObject.SERIALS_UNIQUENESS_GLOBAL))
		{
			return !SelectedWarehouseData.getInstance().getItemsForSerials().containsKey(serial.getSerialNr().trim().toLowerCase()) && !isAlreadyAdded(serial);
		}
		else
		{
			return (!SelectedWarehouseData.getInstance().getSerialsForItems().containsKey(item.getId()) || !SelectedWarehouseData.getInstance().getSerialsForItems().get(item.getId()).contains(serial.getSerialNr())) && !isAlreadyAdded(serial);
		}
	}

	private void addSerial(String serialString){
		SerialNumber serial = new SerialNumber();
		if (isModeAddSerial()) {
			serial.setSerialNr(serialString);
			if (isSerialOk(serial,SelectedWarehouseData.getInstance().getCurrentDocElement().getItem()))
			{
				addSerial(serial);
			} else {
				mListener.showSerialDialogAlreadyExists(serial);
			}
		}
		else
		{
			serial.setSerialNr(serialString);
			//no need to check if serial already is new, it must already exist here
			if (!isAlreadyAdded(serial)) {


				addSerial(serial);
			}
			else
			{
				mListener.showSerialDialogAlreadyExists(serial);
			}
		}
	}

	private void addAddButtonListener() {
		relativeLayoutAddButton.setOnClickListener(new View.OnClickListener() {
			@Override
			public void onClick(View v) {

				if (isModeAddSerial())
				{
					addSerial(editTextAddSerial.getText().toString());
				}
				else
				{
					addSerial(autoCompleteTextViewSerial.getText().toString());
				}

			}
		});
	}

	private void addCancelButtonListener() {
		relativeLayoutCancelButton.setOnClickListener(new View.OnClickListener() {
			@Override
			public void onClick(View v) {

				mListener.onSerialNrCancelClicked(SelectedWarehouseData.getInstance().getCurrentDocElement(),finishAfter);
				SelectedWarehouseData.getInstance().setSerialNumbersList(null);
			}
		});
	}

	private void addOkButtonListener() {
		relativeLayoutOkButton.setOnClickListener(new View.OnClickListener() {
			@Override
			public void onClick(View v) {

				SelectedWarehouseData.getInstance().setAdding(isModeAddSerial());
				Collection<SerialNumber> serials = adapter.getSerials();
				if ((isInv()) && isModeAddSerial() && SelectedWarehouseData.getInstance().getSerialsForItems().containsKey(SelectedWarehouseData.getInstance().getCurrentDocElement().getItem().getId()))
				{
					for (SerialNumber serialNumber: SelectedWarehouseData.getInstance().getSerialsForItems().get(SelectedWarehouseData.getInstance().getCurrentDocElement().getItem().getId()))
					{
						serials.add(serialNumber);
					}
				}

				// Validate dedicated columns before saving
				if (!validateSerialNumbersDedicatedColumns(serials)) {
					return; // Validation failed, don't proceed
				}

				mListener.onSerialNrOkClicked(serials,SelectedWarehouseData.getInstance().getCurrentDocElement(),finishAfter);
				SelectedWarehouseData.getInstance().setSerialNumbersList(null);
			}
		});
	}

	private void addSkipButtonListener() {
		relativeLayoutSkipButton.setOnClickListener(new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				SelectedWarehouseData.getInstance().setAdding(isModeAddSerial());
				mListener.onSerialNrOkClicked(new ArrayList<SerialNumber>(),SelectedWarehouseData.getInstance().getCurrentDocElement(),finishAfter);
				SelectedWarehouseData.getInstance().setSerialNumbersList(null);
			}
		});
	}

	private void addAddAutoButtonListener() {
		relativeLayoutAddAuto.setOnClickListener(new View.OnClickListener() {
			@Override
			public void onClick(View v) {
				if (adapter.getSerials().isEmpty())
				{
					int count = 0;
					for (SerialNumber serial:SelectedWarehouseData.getInstance().getAvaliableSerialNrList())
					{
						addSerial(serial);
						count++;
						if (count>=how_much_required)
							break;
					}
				}
			}
		});
	}

	private void addScanButtonListener() {
		imageViewScan.setOnClickListener(new View.OnClickListener() {
			@Override
			public void onClick(View v) {

				mListener.onSerialScanButtonClicked();

			}
		});
	}


	private void addSearchButtonListener() {
		imageSearchForSerial.setOnClickListener(new View.OnClickListener() {
			@Override
			public void onClick(View v) {

				mListener.onSearchSerialButtonClicked();

			}
		});
	}

	private void addListeners()
	{
		addEditTextChangeListener();
		addAddButtonListener();
		addOkButtonListener();
		addCancelButtonListener();
		addScanButtonListener();
		addAutoTextViewChangeListener();
		addSearchButtonListener();
		addAddAutoButtonListener();
		addSkipButtonListener();
	}

	@Override
	public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {

		View view = inflater.inflate(R.layout.fragment_add_serial_numbers, null);

		textViewRemainingSerials = (TextView) view.findViewById(R.id.textViewRemainingSerialNr);
		imageViewScan = (ImageView) view.findViewById(R.id.imageViewScanCode);
		editTextAddSerial = (EditText) view.findViewById(R.id.editTextSerialNr);
		relativeLayoutAddButton = (RelativeLayout) view.findViewById(R.id.relativeLayoutButtonAdd);
		relativeLayoutAddAuto = (RelativeLayout) view.findViewById(R.id.relativeLayoutButtonAddAuto);
		autoCompleteTextViewSerial = (AutoCompleteTextView)view.findViewById(R.id.autoCompleteTextViewSerialNr);
		listViewSerialNumbers = (ListView) view.findViewById(R.id.listViewSerialNumbers);
		textViewNoSerialError = (TextView) view.findViewById(R.id.textViewNoSerialSerialNr);
		imageSearchForSerial = (ImageView) view.findViewById(R.id.imageViewSearchSerialNr);
		textViewAddAuto = (TextView) view.findViewById(R.id.textViewAddAuto);
		textViewAddButton = (TextView) view.findViewById(R.id.textViewAdd);

		View footerView = ((LayoutInflater) getActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.footer_serial_list, null, false);
		relativeLayoutCancelButton = (RelativeLayout) footerView.findViewById(R.id.relativeLayoutButtonCancel);
		relativeLayoutOkButton = (RelativeLayout) footerView.findViewById(R.id.relativeLayoutButtonOk);
		relativeLayoutSkipButton = (RelativeLayout) footerView.findViewById(R.id.relativeLayoutButtonSkip);
		textViewSkipButton = (TextView) footerView.findViewById(R.id.textViewSkip);
		textViewOkButton = (TextView) footerView.findViewById(R.id.textViewOk);
		textViewCancelButton = (TextView) footerView.findViewById(R.id.textViewCancel);
		listViewSerialNumbers.addFooterView(footerView);

		init(getArguments());
		setVisibility();

		addListeners();

		ArrayList<SerialNumber> list = (ArrayList<SerialNumber>) SelectedWarehouseData.getInstance().getSerialNumbersList();
		if (list == null) {
			list = new ArrayList<SerialNumber>();
			SelectedWarehouseData.getInstance().setSerialNumbersList(list);
		}
		onSerialNrChanged(list.size());
		adapter = new SerialNrListAdapter(getActivity(), new ArrayList<SerialNumber>(list), this);
		listViewSerialNumbers.setAdapter(adapter);

		Log.e(LomagApplication.APP_TAG,"AddSerialNumbersFragment - - isModeAddSerial:"+isModeAddSerial());
		Log.e(LomagApplication.APP_TAG,"AddSerialNumbersFragment -  - isModeRemoveSerial:"+isModeRemoveSerial());

		if (isModeRemoveSerial())
		{
			startGetDeliveryItemProcedure(
					SelectedWarehouseData.getInstance().getCurrentDocElement().getItem(),
					SelectedWarehouseData.getInstance().getCurrentDocument().getDate());
		}

		editTextAddSerial.requestFocus();

		mListener.onSerialFragmentCreated(this);

		return view;
	}

	@Override
	public void onAttach(Context context) {
		super.onAttach(context);

		try {
			mainActivity = (MainActivity) context;
			mListener = (AddSerialFragmentListener) context;
		}
		catch (ClassCastException e) {
			throw new ClassCastException(context.toString() + " must implement AddSerialFragmentListener");
		}
	}

    @Override
    public void onAttach(Activity context) {
        super.onAttach(context);

        try {
			mainActivity = (MainActivity) context;
            mListener = (AddSerialFragmentListener) context;
        }
        catch (ClassCastException e) {
            throw new ClassCastException(context.toString() + " must implement AddSerialFragmentListener");
        }
    }
	@Override
	public void onDestroyView() {
		super.onDestroyView();
	}

	@Override
	public void onSaveInstanceState(Bundle b) {
		super.onSaveInstanceState(b);
	}

	public void onSerialScanned(String serial)
	{
		addSerial(serial);
	}

	private boolean isModeAddSerial()
	{
		Log.e(LomagApplication.APP_TAG,"AddSerialNumbersFragment - - isModeAddSerial: "+isRecepit() +" "+isInv()+" "+quant_diff);
		return isRecepit() || isInv() && quant_diff<0;
	}

	private boolean isModeRemoveSerial()
	{
		Log.e(LomagApplication.APP_TAG,"AddSerialNumbersFragment - - isModeRemoveSerial: "+isIssue() +" "+isInv()+" "+quant_diff);
		return isIssue() || isInv() && quant_diff>0;
	}

	/**
	 * Validate dedicated columns for all serial numbers
	 * @param serials Collection of serial numbers to validate
	 * @return true if all mandatory dedicated columns are filled, false otherwise
	 */
	private boolean validateSerialNumbersDedicatedColumns(Collection<SerialNumber> serials) {
		try {
			// Start procedure to get dedicated columns for SerialNumbersDC table
			GetDedicatedColumnProcedure procedure = new GetDedicatedColumnProcedure("SerialNumbersDC", null);
			ProcedureExecutionAsyncTask procedureAsyncTask = new ProcedureExecutionAsyncTask(procedure, new ProcedureExecutionAsyncTask.ProcedureExecutionListener() {
				@Override
				public void onProcedureExecuted(Object result, Procedure procedure) {
					try {
						GetDedicatedColumnProcedure dcProcedure = (GetDedicatedColumnProcedure) procedure;
						ArrayList<DedicatedColumn> dedicatedColumns = dcProcedure.getDedicatedColumn_List();

						if (dedicatedColumns != null && !dedicatedColumns.isEmpty()) {
							// Check if any dedicated column is mandatory using utility class
							if (DedicatedColumnsValidator.hasMandatoryColumns(dedicatedColumns)) {
								// Check each serial number for mandatory dedicated columns
								for (SerialNumber serial : serials) {
									if (!DedicatedColumnsValidator.validateSingleSerialDedicatedColumns(serial, dedicatedColumns, getActivity())) {
										return; // Validation failed
									}
								}
							}
						}

						// If we reach here, validation passed - proceed with save
						mListener.onSerialNrOkClicked(serials, SelectedWarehouseData.getInstance().getCurrentDocElement(), finishAfter);
						SelectedWarehouseData.getInstance().setSerialNumbersList(null);

					} catch (Exception e) {
						Log.e(LomagApplication.APP_TAG, "Error validating dedicated columns: " + e);
						// On error, proceed without validation
						mListener.onSerialNrOkClicked(serials, SelectedWarehouseData.getInstance().getCurrentDocElement(), finishAfter);
						SelectedWarehouseData.getInstance().setSerialNumbersList(null);
					}
				}

				@Override
				public void onConnectionFailed(Procedure procedure, ProcedureExecutionAsyncTask.ProcedureExecutionListener listener, Exception e) {
					Log.e(LomagApplication.APP_TAG, "Failed to get dedicated columns: " + e);
					// On error, proceed without validation
					mListener.onSerialNrOkClicked(serials, SelectedWarehouseData.getInstance().getCurrentDocElement(), finishAfter);
					SelectedWarehouseData.getInstance().setSerialNumbersList(null);
				}
			});
			procedureAsyncTask.execute();
			return false; // Return false to prevent immediate execution, validation will handle the callback

		} catch (Exception e) {
			Log.e(LomagApplication.APP_TAG, "Error starting dedicated columns validation: " + e);
			return true;
		}
	}
}
