package com.longint.lomag.lomagweb.adapters;

import android.content.Context;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.BackgroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.longint.lomag.lomagweb.LomagApplication;
import com.longint.lomag.lomagweb.R;
import com.longint.lomag.lomagweb.data.DefaultDataMemory;
import com.longint.lomag.lomagweb.data.SelectedWarehouseData;
import com.longint.lomag.lomagweb.db.toobjects.DocumentType;
import com.longint.lomag.lomagweb.db.toobjects.StockItem;
import com.longint.lomag.lomagweb.fragments.SettingsFragment;
import com.longint.lomag.lomagweb.utils.DialogUtils;
import com.longint.lomag.lomagweb.utils.Permissions;
import com.nostra13.universalimageloader.core.ImageLoader;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

public class WarehouseItemsForDocumentAdapter extends BaseAdapter {

    private int documentTypeId;
    private LinkedList<StockItem> mStockArray;
    private List<Integer> mBackground;
    private int dataVersion;
    private View footerView;
    private String searchText;

    private final Context mContext;

    private WarehouseItemsForDocumentListListener listener;
    Set<String> permissions;

    public interface WarehouseItemsForDocumentListListener {
        void onDeleteButtonClicked(StockItem item);
        void onEditButtonClicked(StockItem item);
        void onItemClicked(StockItem item);
    }

    public  WarehouseItemsForDocumentAdapter(Context context, List<StockItem> aStockArray, View footerView, WarehouseItemsForDocumentListListener listener,
                                             int documentTypeId) {
        mStockArray = (LinkedList<StockItem>) aStockArray;
        mContext = context;
        dataVersion = 0;
        this.footerView = footerView;
        this.listener = listener;
        this.documentTypeId = documentTypeId;
        DefaultDataMemory ddm = new DefaultDataMemory(context);
        permissions = ddm.getPerrmissions();
        this.mBackground = SelectedWarehouseData.getInstance().getBackgroundDocument();
    }

    public void addItems(List<StockItem> newStockData, int version, String searchText) {
        if (dataVersion == version) {
            mStockArray.addAll(newStockData);
            Collections.sort(mStockArray, new Comparator<StockItem>() {
                @Override
                public int compare(StockItem lhs, StockItem rhs) {
                    return lhs.getName().toUpperCase().compareTo(rhs.getName().toUpperCase());
                }
            });
            notifyDataSetChanged();
            footerView.setVisibility(View.GONE);
            this.searchText = searchText;
        }
    }

    public void addItems(List<StockItem> newStockData, int version) {
        if (dataVersion == version) {
            mStockArray.addAll(newStockData);
//            Collections.sort(mStockArray, new Comparator<StockItem>() {
//                @Override
//                public int compare(StockItem lhs, StockItem rhs) {
//                    return lhs.getName().toUpperCase().compareTo(rhs.getName().toUpperCase());
//                }
//            });
            notifyDataSetChanged();
            footerView.setVisibility(View.GONE);
            //  this.searchText = searchText;
        }
    }

    public void setItem(int pos, StockItem item) {
        mStockArray.set(pos, item);
        notifyDataSetChanged();
    }

    public void removeAll() {
        mStockArray = new LinkedList<StockItem>();
        dataVersion++;
    }

    @Override
    public int getCount() {
        return mStockArray.size();
    }

    @Override
    public Object getItem(int position) {
        return mStockArray.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    private void findViews(View view, WarehouseItemsForDocumentAdapter.StockHolder holder) {
        holder.textViewName = view.findViewById(R.id.textViewItemName);
        holder.imageViewDelete = view.findViewById(R.id.imageViewItemDelete);
        holder.imageViewItemEdit = view.findViewById(R.id.imageViewItemEdit);
        holder.textViewBarcode = view.findViewById(R.id.textViewItemBarcode);
        holder.textViewCount = view.findViewById(R.id.textViewItemCount);
        holder.textViewPrice = view.findViewById(R.id.textViewItemPrice);
        holder.textViewPricaeLabel = view.findViewById(R.id.textViewItemPriceLabel);
        holder.textViewRemarks = view.findViewById(R.id.textViewRemarks);
        holder.ll_ItemRemarks = view.findViewById(R.id.ll_ItemRemarks);
        holder.llItem = view.findViewById(R.id.llItem);

        holder.imageViewPhoto = view.findViewById(R.id.imageViewPhoto);
    }

    private void setData(StockItem item, WarehouseItemsForDocumentAdapter.StockHolder holder)
    {
        Log.e(LomagApplication.APP_TAG, "setData item getIdWareForDocument "+item.getIdWareForDocument());

        if(TextUtils.isEmpty(item.getRemarks()))
        {
            holder.ll_ItemRemarks.setVisibility(LinearLayout.GONE);
            holder.textViewRemarks.setText("");
        }
        else
        {
            holder.ll_ItemRemarks.setVisibility(LinearLayout.VISIBLE);
            holder.textViewRemarks.setText(item.getRemarks());
        }

        holder.textViewPrice.setText(String.format("%.2f", item.getInitialPrice()));
        holder.textViewCount.setText(String.format("%.2f %s", item.getInitialState(), item.getMeassureUnit().getName()));

        SpannableString textViewName = colorText(item.getName());
        SpannableString textViewBarcode = colorText(item.getBarcode());

        holder.textViewName.setText(textViewName);
        holder.textViewBarcode.setText(textViewBarcode);
        if (item.getImage() != null)
        {
            holder.imageViewPhoto.setVisibility(View.VISIBLE);
            ImageLoader.getInstance().displayImage("file://" + item.getImage().getAbsolutePath(), holder.imageViewPhoto);
        } else
            holder.imageViewPhoto.setVisibility(View.GONE);

        /*boolean maUprawnienie = sprawdzUprawnienieEdycja(this.documentTypeId);
        if (!maUprawnienie) {
            holder.imageViewItemEdit.setVisibility(View.GONE);
        }
        else {
            holder.imageViewItemEdit.setVisibility(View.VISIBLE);
        }*/
        if (!permissions.contains(Permissions.PERMISSION_EDIT_ITEM)) {
            holder.imageViewItemEdit.setVisibility(View.GONE);
        }
        else {
            holder.imageViewItemEdit.setVisibility(View.GONE);
            if (item.getDocumentType() != null) {
                int documentTypeId = item.getDocumentType().getId();
                if(documentTypeId == DocumentType.CUSTOMER_ORDER_ZO_ID || documentTypeId == DocumentType.OFFER_OF_ID) {
                    holder.imageViewItemEdit.setVisibility(View.VISIBLE);
                }
            }
        }
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        final StockItem itemData = mStockArray.get(position);
        WarehouseItemsForDocumentAdapter.StockHolder holder = new WarehouseItemsForDocumentAdapter.StockHolder();
        if (convertView == null) {

            LayoutInflater inflater = (LayoutInflater) this.mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            convertView = inflater.inflate(R.layout.list_item_stock_element_for_document, null);

            findViews(convertView, holder);

            if (!SettingsFragment.isPriceIncluded(mContext) || !SelectedWarehouseData.getInstance().getPriceShow()) {
                holder.textViewPrice.setVisibility(View.GONE);
                holder.textViewPricaeLabel.setVisibility(View.GONE);
            } else {
                holder.textViewPrice.setVisibility(View.VISIBLE);
                holder.textViewPricaeLabel.setVisibility(View.VISIBLE);
            }
            holder.imageViewDelete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Integer pos = (Integer) v.getTag();
                  //  listener.onDeleteButtonClicked(mStockArray.get(pos));
                   // SelectedWarehouseData.getInstance().setEditedItemPos(pos);
                    listener.onDeleteButtonClicked(mStockArray.get(pos));
                }
            });

            holder.imageViewItemEdit.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Integer pos = (Integer) v.getTag();

                    StockItem item = mStockArray.get(pos);
                    mBackground.add(item.getId());
                    SelectedWarehouseData.getInstance().setBackgroundDocument(mBackground);
                    listener.onEditButtonClicked(item);
                }
            });

            holder.llItem.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Integer pos = (Integer) v.getTag();

                    StockItem item = mStockArray.get(pos);
                    mBackground.add(item.getId());
                    SelectedWarehouseData.getInstance().setBackgroundDocument(mBackground);
                    listener.onItemClicked(item);
                }
            });

            holder.imageViewPhoto.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Integer pos = (Integer) v.getTag();

                    File photo = mStockArray.get(pos).getImage();
                    if (photo != null) {
                        DialogUtils.showImageDialog(mContext, photo.getAbsolutePath());
                    }
                }
            });
        } else {
            holder = (WarehouseItemsForDocumentAdapter.StockHolder) convertView.getTag();
        }

        setData(itemData, holder);
        holder.imageViewDelete.setTag(position);
        holder.imageViewItemEdit.setTag(position);
        holder.llItem.setTag(position);

        if (mBackground.contains(itemData.getId()))
        {
            holder.llItem.setBackgroundColor(Color.parseColor("#bab8b8"));
        }
        else
        {
            holder.llItem.setBackgroundColor(Color.parseColor("#ffffff"));
        }

        holder.imageViewPhoto.setTag(position);
        setPermissions(holder);
        convertView.setTag(holder);
        return convertView;
    }

    public int getDataVersion() {
        return dataVersion;
    }

    private class StockHolder {
        protected LinearLayout llItem;
        protected ImageView imageViewDelete;
        protected ImageView imageViewItemEdit;
        protected ImageView imageViewPhoto;
        protected TextView textViewName;
        protected TextView textViewPrice;
        protected TextView textViewCount;
        protected TextView textViewBarcode;
        protected TextView textViewRemarks;
        protected LinearLayout ll_ItemRemarks;
        protected TextView textViewPricaeLabel;
    }

    private SpannableString colorText(String s) {
        SpannableString text = new SpannableString(s);
        int start;
        int length;
        if (searchText != null && (length = searchText.length()) > 0
                && (start = s.toLowerCase().indexOf(searchText.toLowerCase())) != -1)
            text.setSpan(new BackgroundColorSpan(mContext.getResources()
                    .getColor(R.color.green)), start, start + length, 0);
        return text;
    }

    private void setPermissions(WarehouseItemsForDocumentAdapter.StockHolder holder) {
        boolean maUprawnienie = sprawdzUprawnienieUsuwanie(this.documentTypeId);
        if (!maUprawnienie) {
            holder.imageViewDelete.setVisibility(View.GONE);
        }
        else {
            holder.imageViewDelete.setVisibility(View.VISIBLE);
        }
    }

    private boolean sprawdzUprawnienieUsuwanie(int documentTypeId) {

        boolean result = false;
        if(documentTypeId== DocumentType.RECEIVE_ID && permissions.contains(Permissions.PERMISSION_DELETE_PZ))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.RELEASE_ID && permissions.contains(Permissions.PERMISSION_DELETE_WZ))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.INTERBRANCH_TRANSFER_ID && permissions.contains(Permissions.PERMISSION_DELETE_MM))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.INTERNAL_GOODS_RW_ID && permissions.contains(Permissions.PERMISSION_DELETE_RW))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.INTERNAL_GOODS_PW_ID && permissions.contains(Permissions.PERMISSION_DELETE_PW))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.CUSTOMER_ORDER_ZO_ID && permissions.contains(Permissions.PERMISSION_DELETE_ZO))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.OFFER_OF_ID && permissions.contains(Permissions.PERMISSION_DELETE_OF))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.INVOICE_OF_ID && permissions.contains(Permissions.PERMISSION_DELETE_FV))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.CORRECTION_INVOICE_OF_ID && permissions.contains(Permissions.PERMISSION_DELETE_FK))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.PROFORMA_OF_ID && permissions.contains(Permissions.PERMISSION_DELETE_PRO))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.SUPPLIER_ORDER_ZD_ID && permissions.contains(Permissions.PERMISSION_DELETE_ZD))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.CHANGE_LOCATION_ID && permissions.contains(Permissions.PERMISSION_DELETE_ZL))
        {
            result = true;
        }

        return result;
    }

    private boolean sprawdzUprawnienieEdycja(int documentTypeId) {
        boolean result = false;
        if(documentTypeId== DocumentType.RECEIVE_ID && permissions.contains(Permissions.PERMISSION_EDIT_PZ))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.RELEASE_ID && permissions.contains(Permissions.PERMISSION_EDIT_WZ))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.INTERBRANCH_TRANSFER_ID && permissions.contains(Permissions.PERMISSION_EDIT_MM))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.INTERNAL_GOODS_RW_ID && permissions.contains(Permissions.PERMISSION_EDIT_RW))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.INTERNAL_GOODS_PW_ID && permissions.contains(Permissions.PERMISSION_EDIT_PW))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.CUSTOMER_ORDER_ZO_ID && permissions.contains(Permissions.PERMISSION_EDIT_ZO))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.OFFER_OF_ID && permissions.contains(Permissions.PERMISSION_EDIT_OF))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.INVOICE_OF_ID && permissions.contains(Permissions.PERMISSION_EDIT_FV))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.CORRECTION_INVOICE_OF_ID && permissions.contains(Permissions.PERMISSION_EDIT_FK))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.PROFORMA_OF_ID && permissions.contains(Permissions.PERMISSION_EDIT_PRO))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.SUPPLIER_ORDER_ZD_ID && permissions.contains(Permissions.PERMISSION_EDIT_ZD))
        {
            result = true;
        }
        else if(documentTypeId== DocumentType.CHANGE_LOCATION_ID && permissions.contains(Permissions.PERMISSION_EDIT_ZL))
        {
            result = true;
        }

        return result;
    }
}
