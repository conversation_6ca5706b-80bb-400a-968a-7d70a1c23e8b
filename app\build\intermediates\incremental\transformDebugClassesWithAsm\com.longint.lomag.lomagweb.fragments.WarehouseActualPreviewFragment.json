{"className": "com.longint.lomag.lomagweb.fragments.WarehouseActualPreviewFragment", "classAnnotations": [], "interfaces": ["android.content.ComponentCallbacks", "android.view.View$OnCreateContextMenuListener", "androidx.lifecycle.LifecycleOwner", "androidx.lifecycle.ViewModelStoreOwner", "androidx.savedstate.SavedStateRegistryOwner", "com.longint.lomag.lomagweb.adapters.WarehouseItemsListAdapter$WarehouseItemsListListener", "com.longint.lomag.lomagweb.db.resultAsyncTasks.ProcedureExecutionAsyncTask$ProcedureExecutionListener"], "superClasses": ["androidx.fragment.app.Fragment", "java.lang.Object"]}