{"className": "com.longint.lomag.lomagweb.fragments.NewRentalArticlesFragment", "classAnnotations": [], "interfaces": ["android.content.ComponentCallbacks", "android.view.View$OnCreateContextMenuListener", "androidx.lifecycle.LifecycleOwner", "androidx.lifecycle.ViewModelStoreOwner", "androidx.savedstate.SavedStateRegistryOwner", "com.longint.lomag.lomagweb.db.resultAsyncTasks.GetResultInterface", "com.longint.lomag.lomagweb.db.resultAsyncTasks.ProcedureExecutionAsyncTask$ProcedureExecutionListener", "com.longint.lomag.lomagweb.utils.OnKeyDownListener"], "superClasses": ["androidx.fragment.app.Fragment", "java.lang.Object"]}