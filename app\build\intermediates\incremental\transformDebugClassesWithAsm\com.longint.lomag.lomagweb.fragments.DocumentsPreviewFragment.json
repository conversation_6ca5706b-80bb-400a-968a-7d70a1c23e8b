{"className": "com.longint.lomag.lomagweb.fragments.DocumentsPreviewFragment", "classAnnotations": [], "interfaces": ["android.content.ComponentCallbacks", "android.view.View$OnCreateContextMenuListener", "androidx.lifecycle.LifecycleOwner", "androidx.lifecycle.ViewModelStoreOwner", "androidx.savedstate.SavedStateRegistryOwner", "com.longint.lomag.lomagweb.adapters.DocumentsItemsListAdapter$DocumentItemsListListener", "com.longint.lomag.lomagweb.db.resultAsyncTasks.GetResultInterface", "com.longint.lomag.lomagweb.db.resultAsyncTasks.ProcedureExecutionAsyncTask$ProcedureExecutionListener"], "superClasses": ["androidx.fragment.app.Fragment", "java.lang.Object"]}