1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.longint.lomag.lomagweb"
4    android:installLocation="preferExternal"
5    android:versionCode="1428"
6    android:versionName="1.4.28" >
7
8    <uses-sdk
9        android:minSdkVersion="16"
9-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
10        android:targetSdkVersion="34" />
10-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
11
12    <uses-feature
12-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:8:5-85
13        android:name="android.hardware.camera"
13-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:8:19-57
14        android:required="false" />
14-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:8:58-82
15    <uses-feature
15-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:9:5-95
16        android:name="android.hardware.camera.autofocus"
16-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:9:19-67
17        android:required="false" />
17-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:9:68-92
18
19    <uses-permission android:name="android.permission.INTERNET" />
19-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:11:5-67
19-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:11:22-64
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:12:5-79
20-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:12:22-76
21    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
21-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:13:5-76
21-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:13:22-73
22    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
22-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:14:5-75
22-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:14:22-73
23    <uses-permission android:name="android.permission.CAMERA" />
23-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:16:5-65
23-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:16:22-62
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:17:5-81
24-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:17:22-78
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:18:5-80
25-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:18:22-77
26    <uses-permission android:name="android.permission.VIBRATE" />
26-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:19:5-66
26-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:19:22-63
27    <uses-permission android:name="android.permission.FLASHLIGHT" />
27-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:20:5-69
27-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:20:22-66
28
29    <queries>
29-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:78:5-82:15
30        <intent>
30-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:79:9-81:18
31            <action android:name="android.media.action.IMAGE_CAPTURE" />
31-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:80:13-73
31-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:80:21-70
32        </intent>
33    </queries>
34
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:25:5-68
35-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:25:22-65
36    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
36-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:26:5-110
36-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:26:22-107
37    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- // sdsfvs -->
37-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:26:5-82
37-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:26:22-79
38    <!-- android:usesCleartextTraffic="true" -->
39    <!-- android:allowBackup="true" -->
40    <application
40-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:25:5-76:19
41        android:name="com.longint.lomag.lomagweb.LomagApplication"
41-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:27:9-41
42        android:allowBackup="false"
42-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:28:9-36
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\0dae37a976ed46f7b871d598b1d13ce3\transformed\core-1.3.2\AndroidManifest.xml:24:18-86
44        android:debuggable="true"
45        android:icon="@drawable/ic_launcher_new"
45-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:30:9-49
46        android:label="@string/app_name"
46-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:31:9-41
47        android:largeHeap="true"
47-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:29:9-33
48        android:supportsRtl="true"
48-->[:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-35
49        android:testOnly="true"
50        android:theme="@style/Theme.AppCompat.Light.NoActionBar"
50-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:32:9-65
51        android:usesCleartextTraffic="true" >
51-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:26:9-44
52        <activity
52-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:35:9-46:20
53            android:name="com.longint.lomag.lomagweb.MainActivity"
53-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:35:19-47
54            android:configChanges="orientation|keyboardHidden|screenSize"
54-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:36:13-74
55            android:exported="true"
55-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:40:17-40
56            android:label="@string/app_name"
56-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:37:13-45
57            android:theme="@style/AppTheme_Main"
57-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:39:17-53
58            android:windowSoftInputMode="adjustPan" >
58-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:38:13-52
59            <intent-filter>
59-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:42:13-45:29
60                <action android:name="android.intent.action.MAIN" />
60-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:43:17-69
60-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:43:25-66
61
62                <category android:name="android.intent.category.LAUNCHER" />
62-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:44:17-77
62-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:44:27-74
63            </intent-filter>
64        </activity>
65        <activity
65-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:48:9-52:71
66            android:name="com.longint.lomag.lomagweb.ScanningActivity"
66-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:49:13-45
67            android:configChanges="orientation|keyboardHidden"
67-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:50:13-63
68            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
68-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:52:13-69
69            android:windowSoftInputMode="stateAlwaysHidden" />
69-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:51:13-60
70
71        <provider
71-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:54:9-62:20
72            android:name="com.longint.lomag.lomagweb.utils.GenericFileProvider"
72-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:55:13-54
73            android:authorities="com.longint.lomag.lomagweb.fileprovider"
73-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:56:13-74
74            android:exported="false"
74-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:57:13-37
75            android:grantUriPermissions="true" >
75-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:58:13-47
76            <meta-data
76-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:59:13-61:64
77                android:name="android.support.FILE_PROVIDER_PATHS"
77-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:60:17-67
78                android:resource="@xml/file_paths" />
78-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:61:17-51
79        </provider>
80
81        <activity
81-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:64:9-73:20
82            android:name="com.longint.lomag.lomagweb.SendLogActivity"
82-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:65:13-44
83            android:exported="true"
83-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:68:13-36
84            android:label="@string/app_name"
84-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:66:13-45
85            android:windowSoftInputMode="adjustPan" >
85-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:67:13-52
86            <intent-filter>
86-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:69:13-72:29
87                <action android:name="com.longint.lomag.lomagweb.SEND_LOG" />
87-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:70:17-78
87-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:70:25-75
88
89                <category android:name="android.intent.category.DEFAULT" />
89-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:71:17-76
89-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:71:27-73
90            </intent-filter>
91        </activity>
92        <activity
92-->[:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-16:63
93            android:name="jim.h.common.android.lib.zxing.CaptureActivity"
93-->[:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-74
94            android:configChanges="orientation|keyboardHidden"
94-->[:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-63
95            android:windowSoftInputMode="stateAlwaysHidden" />
95-->[:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-60
96
97        <service
97-->[com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:13:9-19:19
98            android:name="com.google.firebase.components.ComponentDiscoveryService"
98-->[com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:14:13-84
99            android:directBootAware="true"
99-->[com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:34:13-43
100            android:exported="false" >
100-->[com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:15:13-37
101            <meta-data
101-->[com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:16:13-18:85
102                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
102-->[com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:17:17-115
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:18:17-82
104            <meta-data
104-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:34:13-36:85
105                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
105-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:35:17-109
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:36:17-82
107            <meta-data
107-->[com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:32:13-34:85
108                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
108-->[com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:33:17-117
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:34:17-82
110            <meta-data
110-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:32:13-34:85
111                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
111-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:33:17-96
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:34:17-82
113            <meta-data
113-->[com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:30:13-32:85
114                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
114-->[com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:31:17-139
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:32:17-82
116            <meta-data
116-->[com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:17:13-19:85
117                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
117-->[com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:18:17-127
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:19:17-82
119            <meta-data
119-->[com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:13:13-15:85
120                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
120-->[com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:14:17-109
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:15:17-82
122        </service>
123
124        <provider
124-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:25:9-29:39
125            android:name="com.google.firebase.perf.provider.FirebasePerfProvider"
125-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:26:13-82
126            android:authorities="com.longint.lomag.lomagweb.firebaseperfprovider"
126-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:27:13-72
127            android:exported="false"
127-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:28:13-37
128            android:initOrder="101" />
128-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:29:13-36
129
130        <receiver
130-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:29:9-33:20
131            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
131-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:30:13-85
132            android:enabled="true"
132-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:31:13-35
133            android:exported="false" >
133-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:32:13-37
134        </receiver>
135
136        <service
136-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:35:9-38:40
137            android:name="com.google.android.gms.measurement.AppMeasurementService"
137-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:36:13-84
138            android:enabled="true"
138-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:37:13-35
139            android:exported="false" />
139-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:38:13-37
140        <service
140-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:39:9-43:72
141            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
141-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:40:13-87
142            android:enabled="true"
142-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:41:13-35
143            android:exported="false"
143-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:42:13-37
144            android:permission="android.permission.BIND_JOB_SERVICE" />
144-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:43:13-69
145
146        <receiver
146-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:37:9-44:20
147            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
147-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:38:13-78
148            android:exported="true"
148-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:39:13-36
149            android:permission="com.google.android.c2dm.permission.SEND" >
149-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:40:13-73
150            <intent-filter>
150-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:41:13-43:29
151                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
151-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:42:17-81
151-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:42:25-78
152            </intent-filter>
153        </receiver>
154
155        <activity
155-->[com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:23:9-26:75
156            android:name="com.google.android.gms.common.api.GoogleApiActivity"
156-->[com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:24:13-79
157            android:exported="false"
157-->[com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:25:13-37
158            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
158-->[com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:26:13-72
159
160        <provider
160-->[com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:26:9-30:39
161            android:name="com.google.firebase.provider.FirebaseInitProvider"
161-->[com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:27:13-77
162            android:authorities="com.longint.lomag.lomagweb.firebaseinitprovider"
162-->[com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:28:13-72
163            android:exported="false"
163-->[com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:29:13-37
164            android:initOrder="100" />
164-->[com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:30:13-36
165
166        <meta-data
166-->[com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:23:9-25:69
167            android:name="com.google.android.gms.version"
167-->[com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:24:13-58
168            android:value="@integer/google_play_services_version" />
168-->[com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:25:13-66
169
170        <service
170-->[com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:29:9-35:19
171            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
171-->[com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:30:13-103
172            android:exported="false" >
172-->[com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:31:13-37
173            <meta-data
173-->[com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:32:13-34:39
174                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
174-->[com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:33:17-94
175                android:value="cct" />
175-->[com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:34:17-36
176        </service>
177        <service
177-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:26:9-30:19
178            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
178-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:27:13-117
179            android:exported="false"
179-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:28:13-37
180            android:permission="android.permission.BIND_JOB_SERVICE" >
180-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:29:13-69
181        </service>
182
183        <receiver
183-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:32:9-34:40
184            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
184-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:33:13-132
185            android:exported="false" />
185-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:34:13-37
186    </application>
187
188</manifest>
