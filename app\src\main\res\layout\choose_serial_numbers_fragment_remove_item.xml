<?xml version = "1.0" encoding = "utf-8" ?>
<LinearLayout
        xmlns:android = "http://schemas.android.com/apk/res/android"
        android:layout_width = "match_parent"
        android:layout_height = "wrap_content"
        android:orientation = "horizontal"
        android:weightSum="1.0">
    <TextView
        android:id="@+id/serialNumberText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginLeft="@dimen/material_screen_edge_margin"
        android:layout_marginStart="@dimen/material_screen_edge_margin"
        android:textSize="@dimen/menu_medium_labels"
        android:paddingTop="@dimen/material_flat_button_margin"
        android:paddingBottom="@dimen/material_flat_button_margin"
        android:textAppearance="?android:attr/textAppearanceLarge"/>

    <ImageView
        android:id="@+id/dedicatedColumnsButton"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginRight="@dimen/material_screen_edge_margin"
        android:layout_marginEnd="@dimen/material_screen_edge_margin"
        android:layout_gravity="center_vertical"
        android:src="@drawable/ic_folder_dc"
        android:scaleType="fitCenter"
        android:contentDescription="@string/selected_warehouse_content_discribtion"/>

    <ImageView
        android:id="@+id/removeSerialNumberButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/material_screen_edge_margin"
        android:layout_marginEnd="@dimen/material_screen_edge_margin"
        android:src="@drawable/ic_remove_item"
        android:contentDescription="@string/selected_warehouse_content_discribtion"/>
</LinearLayout>