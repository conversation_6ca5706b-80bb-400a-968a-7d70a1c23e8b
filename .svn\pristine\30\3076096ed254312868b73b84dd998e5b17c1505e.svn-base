package com.longint.lomag.lomagweb.db.procedures.get;

import android.util.Log;

import com.longint.lomag.lomagweb.LomagApplication;
import com.longint.lomag.lomagweb.db.procedures.Procedure;
import com.longint.lomag.lomagweb.db.toobjects.SerialNumber;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

/**
 * Procedure for getting serial numbers for a specific item in a document
 */
public class GetSerialsForItemInDocumentProcedure implements Procedure {

    private PreparedStatement _statement;
    private int itemIdInDocument;
    private int documentId;
    private ArrayList<SerialNumber> serialNumbers;

    public GetSerialsForItemInDocumentProcedure(int itemIdInDocument, int documentId) {
        this.itemIdInDocument = itemIdInDocument;
        this.documentId = documentId;
        this.serialNumbers = new ArrayList<>();
    }

    @Override
    public Object executeProcedure(Connection connection) throws SQLException {
        try {
            Log.e(LomagApplication.APP_TAG, "GetSerialsForItemInDocumentProcedure - itemIdInDocument: " + itemIdInDocument + ", documentId: " + documentId);

            // Query to get serial numbers for specific item in document
            String query = "SELECT sn.IDSerialNumber, sn.Number, sn.IDElementuRuchuMagazynowego " +
                          "FROM SerialNumbers sn " +
                          "INNER JOIN ElementRuchuMagazynowego el ON sn.IDElementuRuchuMagazynowego = el.IDElementuRuchuMagazynowego " +
                          "INNER JOIN RuchMagazynowy r ON el.IDRuchuMagazynowego = r.IDRuchuMagazynowego " +
                          "WHERE el.IDElementuRuchuMagazynowego = ? AND r.IDRuchuMagazynowego = ?";

            _statement = connection.prepareStatement(query);
            _statement.setInt(1, itemIdInDocument);
            _statement.setInt(2, documentId);

            ResultSet result = _statement.executeQuery();

            if (result != null) {
                while (result.next()) {
                    SerialNumber serial = new SerialNumber();
                    serial.setId(result.getInt("IDSerialNumber"));
                    serial.setSerialNr(result.getString("Number"));
                    serial.setDocElementId(result.getInt("IDElementuRuchuMagazynowego"));

                    serialNumbers.add(serial);
                    Log.e(LomagApplication.APP_TAG, "GetSerialsForItemInDocumentProcedure - found serial: " + serial.getSerialNr());
                }
            }

            Log.e(LomagApplication.APP_TAG, "GetSerialsForItemInDocumentProcedure - total serials found: " + serialNumbers.size());
            return serialNumbers;

        } catch (Exception e) {
            Log.e(LomagApplication.APP_TAG, "Error in GetSerialsForItemInDocumentProcedure: " + e.toString());
            throw e;
        }
    }

    public ArrayList<SerialNumber> getSerialNumbers() {
        return serialNumbers;
    }
}
