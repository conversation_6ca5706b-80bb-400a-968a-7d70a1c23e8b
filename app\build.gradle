apply plugin: 'com.android.application'
// Add the Firebase Crashlytics plugin.
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.firebase.firebase-perf'

android {
    compileSdkVersion 31

    defaultConfig {
        applicationId "com.longint.lomag.lomagweb"
        minSdkVersion 16
        targetSdk 34
        versionCode 1429
        versionName '1.4.29'
        multiDexEnabled true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables.useSupportLibrary = true
    }


    buildTypes {
        release {

            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.txt'
        }
    }
    packagingOptions {
        resources {
            excludes += ['LICENSE.txt']
        }
    }
//  sourceSets { main { java.srcDirs = ['src/main/java', 'src/AndroidTests', 'src/AndroidTest/java']  } }

    useLibrary 'org.apache.http.legacy'


    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    lint {
        abortOnError false
        checkReleaseBuilds false
        disable 'MissingTranslation'
    }

    //   testOptions {
 //       unitTests.returnDefaultValues = true
//    }
}

final RUNNER_VERSION = '0.4'
final ESPRESSO_VERSION = '2.2.1'

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation fileTree(include: ['*.aar'], dir: 'libs')
    implementation 'org.jetbrains:annotations:15.0'

//    implementation files('libs/jtds-1.2.7.jar')
//    testImplementation files('libs/jtds-1.2.7.jar')

    testImplementation 'junit:junit:4.12'
    implementation 'androidx.appcompat:appcompat:1.0.0'
    implementation 'androidx.preference:preference:1.0.0'
    implementation 'com.google.android.material:material:1.0.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.multidex:multidex:2.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'com.google.code.gson:gson:2.8.5'

    implementation 'com.nostra13.universalimageloader:universal-image-loader:1.9.5'
    androidTestImplementation 'androidx.test:runner:1.1.0'
    androidTestImplementation 'androidx.test:rules:1.1.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.0'
    androidTestImplementation 'androidx.test.espresso:espresso-intents:3.1.0'
    androidTestImplementation 'org.mockito:mockito-core:1.10.19'
    implementation project(':zxinglib')

    implementation group: 'com.google.zxing', name: 'core', version: '3.2.1'

// Recommended: Add the Google Analytics SDK.
    implementation 'com.google.firebase:firebase-analytics:17.5.0'
    // Add the Firebase Crashlytics SDK.
    implementation 'com.google.firebase:firebase-crashlytics:17.0.1'

    // Add the dependency for the Performance Monitoring library
    implementation 'com.google.firebase:firebase-perf:19.0.7'
    implementation 'com.github.Kunzisoft:Android-SwitchDateTimePicker:2.0'

}
apply plugin: 'com.google.gms.google-services'
