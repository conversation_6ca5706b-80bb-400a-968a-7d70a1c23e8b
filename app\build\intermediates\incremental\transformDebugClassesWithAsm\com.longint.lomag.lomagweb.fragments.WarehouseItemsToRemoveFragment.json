{"className": "com.longint.lomag.lomagweb.fragments.WarehouseItemsToRemoveFragment", "classAnnotations": [], "interfaces": ["android.content.ComponentCallbacks", "android.view.View$OnCreateContextMenuListener", "androidx.lifecycle.LifecycleOwner", "androidx.lifecycle.ViewModelStoreOwner", "androidx.savedstate.SavedStateRegistryOwner"], "superClasses": ["androidx.fragment.app.Fragment", "java.lang.Object"]}