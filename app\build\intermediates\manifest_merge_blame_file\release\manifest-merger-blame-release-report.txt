1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.longint.lomag.lomagweb"
4    android:installLocation="preferExternal"
5    android:versionCode="1428"
6    android:versionName="1.4.28" >
7
8    <uses-sdk
9        android:minSdkVersion="16"
9-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
10        android:targetSdkVersion="34" />
10-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml
11
12    <uses-feature
12-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:8:5-85
13        android:name="android.hardware.camera"
13-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:8:19-57
14        android:required="false" />
14-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:8:58-82
15    <uses-feature
15-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:9:5-95
16        android:name="android.hardware.camera.autofocus"
16-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:9:19-67
17        android:required="false" />
17-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:9:68-92
18
19    <uses-permission android:name="android.permission.INTERNET" />
19-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:11:5-67
19-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:11:22-64
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:12:5-79
20-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:12:22-76
21    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
21-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:13:5-76
21-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:13:22-73
22    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
22-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:14:5-75
22-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:14:22-73
23    <uses-permission android:name="android.permission.CAMERA" />
23-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:16:5-65
23-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:16:22-62
24    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
24-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:17:5-81
24-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:17:22-78
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:18:5-80
25-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:18:22-77
26    <uses-permission android:name="android.permission.VIBRATE" />
26-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:19:5-66
26-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:19:22-63
27    <uses-permission android:name="android.permission.FLASHLIGHT" />
27-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:20:5-69
27-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:20:22-66
28
29    <queries>
29-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:78:5-82:15
30        <intent>
30-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:79:9-81:18
31            <action android:name="android.media.action.IMAGE_CAPTURE" />
31-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:80:13-73
31-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:80:21-70
32        </intent>
33    </queries>
34
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:25:5-68
35-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:25:22-65
36    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
36-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:26:5-110
36-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:26:22-107
37    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- // sdsfvs -->
37-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:26:5-82
37-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:26:22-79
38    <!-- android:usesCleartextTraffic="true" -->
39    <!-- android:allowBackup="true" -->
40    <application
40-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:25:5-76:19
41        android:name="com.longint.lomag.lomagweb.LomagApplication"
41-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:27:9-41
42        android:allowBackup="false"
42-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:28:9-36
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\0dae37a976ed46f7b871d598b1d13ce3\transformed\core-1.3.2\AndroidManifest.xml:24:18-86
44        android:icon="@drawable/ic_launcher_new"
44-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:30:9-49
45        android:label="@string/app_name"
45-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:31:9-41
46        android:largeHeap="true"
46-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:29:9-33
47        android:supportsRtl="true"
47-->[:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\release\AndroidManifest.xml:11:9-35
48        android:theme="@style/Theme.AppCompat.Light.NoActionBar"
48-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:32:9-65
49        android:usesCleartextTraffic="true" >
49-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:26:9-44
50        <activity
50-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:35:9-46:20
51            android:name="com.longint.lomag.lomagweb.MainActivity"
51-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:35:19-47
52            android:configChanges="orientation|keyboardHidden|screenSize"
52-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:36:13-74
53            android:exported="true"
53-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:40:17-40
54            android:label="@string/app_name"
54-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:37:13-45
55            android:theme="@style/AppTheme_Main"
55-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:39:17-53
56            android:windowSoftInputMode="adjustPan" >
56-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:38:13-52
57            <intent-filter>
57-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:42:13-45:29
58                <action android:name="android.intent.action.MAIN" />
58-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:43:17-69
58-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:43:25-66
59
60                <category android:name="android.intent.category.LAUNCHER" />
60-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:44:17-77
60-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:44:27-74
61            </intent-filter>
62        </activity>
63        <activity
63-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:48:9-52:71
64            android:name="com.longint.lomag.lomagweb.ScanningActivity"
64-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:49:13-45
65            android:configChanges="orientation|keyboardHidden"
65-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:50:13-63
66            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
66-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:52:13-69
67            android:windowSoftInputMode="stateAlwaysHidden" />
67-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:51:13-60
68
69        <provider
69-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:54:9-62:20
70            android:name="com.longint.lomag.lomagweb.utils.GenericFileProvider"
70-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:55:13-54
71            android:authorities="com.longint.lomag.lomagweb.fileprovider"
71-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:56:13-74
72            android:exported="false"
72-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:57:13-37
73            android:grantUriPermissions="true" >
73-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:58:13-47
74            <meta-data
74-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:59:13-61:64
75                android:name="android.support.FILE_PROVIDER_PATHS"
75-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:60:17-67
76                android:resource="@xml/file_paths" />
76-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:61:17-51
77        </provider>
78
79        <activity
79-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:64:9-73:20
80            android:name="com.longint.lomag.lomagweb.SendLogActivity"
80-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:65:13-44
81            android:exported="true"
81-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:68:13-36
82            android:label="@string/app_name"
82-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:66:13-45
83            android:windowSoftInputMode="adjustPan" >
83-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:67:13-52
84            <intent-filter>
84-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:69:13-72:29
85                <action android:name="com.longint.lomag.lomagweb.SEND_LOG" />
85-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:70:17-78
85-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:70:25-75
86
87                <category android:name="android.intent.category.DEFAULT" />
87-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:71:17-76
87-->C:\vs\LomagWebAndroidStudio\app\src\main\AndroidManifest.xml:71:27-73
88            </intent-filter>
89        </activity>
90        <activity
90-->[:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\release\AndroidManifest.xml:13:9-16:63
91            android:name="jim.h.common.android.lib.zxing.CaptureActivity"
91-->[:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\release\AndroidManifest.xml:14:13-74
92            android:configChanges="orientation|keyboardHidden"
92-->[:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\release\AndroidManifest.xml:15:13-63
93            android:windowSoftInputMode="stateAlwaysHidden" />
93-->[:zxinglib] C:\vs\LomagWebAndroidStudio\zxinglib\build\intermediates\merged_manifest\release\AndroidManifest.xml:16:13-60
94
95        <service
95-->[com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:13:9-19:19
96            android:name="com.google.firebase.components.ComponentDiscoveryService"
96-->[com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:14:13-84
97            android:directBootAware="true"
97-->[com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:34:13-43
98            android:exported="false" >
98-->[com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:15:13-37
99            <meta-data
99-->[com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:16:13-18:85
100                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
100-->[com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:17:17-115
101                android:value="com.google.firebase.components.ComponentRegistrar" />
101-->[com.google.firebase:firebase-crashlytics:17.0.1] C:\vs\gradle-5.6.4\caches\transforms-3\867d0214a66084f3741f834a359d8a7e\transformed\jetified-firebase-crashlytics-17.0.1\AndroidManifest.xml:18:17-82
102            <meta-data
102-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:34:13-36:85
103                android:name="com.google.firebase.components:com.google.firebase.perf.FirebasePerfRegistrar"
103-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:35:17-109
104                android:value="com.google.firebase.components.ComponentRegistrar" />
104-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:36:17-82
105            <meta-data
105-->[com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:32:13-34:85
106                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
106-->[com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:33:17-117
107                android:value="com.google.firebase.components.ComponentRegistrar" />
107-->[com.google.firebase:firebase-config:19.0.4] C:\vs\gradle-5.6.4\caches\transforms-3\132ced61eeb02c452b677af3053f35f7\transformed\jetified-firebase-config-19.0.4\AndroidManifest.xml:34:17-82
108            <meta-data
108-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:32:13-34:85
109                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
109-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:33:17-96
110                android:value="com.google.firebase.components.ComponentRegistrar" />
110-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:34:17-82
111            <meta-data
111-->[com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:30:13-32:85
112                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
112-->[com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:31:17-139
113                android:value="com.google.firebase.components.ComponentRegistrar" />
113-->[com.google.android.gms:play-services-measurement-api:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\3d9e7bd224b9b323b6c86aa68e24390b\transformed\jetified-play-services-measurement-api-17.5.0\AndroidManifest.xml:32:17-82
114            <meta-data
114-->[com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:17:13-19:85
115                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
115-->[com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:18:17-127
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[com.google.firebase:firebase-installations:16.3.2] C:\vs\gradle-5.6.4\caches\transforms-3\d83e867a39d8c379e99fcbbc0072c194\transformed\jetified-firebase-installations-16.3.2\AndroidManifest.xml:19:17-82
117            <meta-data
117-->[com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:13:13-15:85
118                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
118-->[com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:14:17-109
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-abt:19.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\ff4dda4f32e1c269119aa31ea19e65dd\transformed\jetified-firebase-abt-19.0.0\AndroidManifest.xml:15:17-82
120        </service>
121
122        <provider
122-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:25:9-29:39
123            android:name="com.google.firebase.perf.provider.FirebasePerfProvider"
123-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:26:13-82
124            android:authorities="com.longint.lomag.lomagweb.firebaseperfprovider"
124-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:27:13-72
125            android:exported="false"
125-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:28:13-37
126            android:initOrder="101" />
126-->[com.google.firebase:firebase-perf:19.0.7] C:\vs\gradle-5.6.4\caches\transforms-3\b8aaab6e9000a9194bdcbdb30f8e3058\transformed\jetified-firebase-perf-19.0.7\AndroidManifest.xml:29:13-36
127
128        <receiver
128-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:29:9-33:20
129            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
129-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:30:13-85
130            android:enabled="true"
130-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:31:13-35
131            android:exported="false" >
131-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:32:13-37
132        </receiver>
133
134        <service
134-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:35:9-38:40
135            android:name="com.google.android.gms.measurement.AppMeasurementService"
135-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:36:13-84
136            android:enabled="true"
136-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:37:13-35
137            android:exported="false" />
137-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:38:13-37
138        <service
138-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:39:9-43:72
139            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
139-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:40:13-87
140            android:enabled="true"
140-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:41:13-35
141            android:exported="false"
141-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:42:13-37
142            android:permission="android.permission.BIND_JOB_SERVICE" />
142-->[com.google.android.gms:play-services-measurement:17.5.0] C:\vs\gradle-5.6.4\caches\transforms-3\619397a17300dc33c46db9d393e778ef\transformed\jetified-play-services-measurement-17.5.0\AndroidManifest.xml:43:13-69
143
144        <receiver
144-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:37:9-44:20
145            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
145-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:38:13-78
146            android:exported="true"
146-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:39:13-36
147            android:permission="com.google.android.c2dm.permission.SEND" >
147-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:40:13-73
148            <intent-filter>
148-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:41:13-43:29
149                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
149-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:42:17-81
149-->[com.google.firebase:firebase-iid:20.1.5] C:\vs\gradle-5.6.4\caches\transforms-3\627d35098273e2b0859fa9b029261d2d\transformed\jetified-firebase-iid-20.1.5\AndroidManifest.xml:42:25-78
150            </intent-filter>
151        </receiver>
152
153        <activity
153-->[com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:23:9-26:75
154            android:name="com.google.android.gms.common.api.GoogleApiActivity"
154-->[com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:24:13-79
155            android:exported="false"
155-->[com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:25:13-37
156            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
156-->[com.google.android.gms:play-services-base:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\34995a864ba69f925217d8a5dff9036d\transformed\jetified-play-services-base-17.0.0\AndroidManifest.xml:26:13-72
157
158        <provider
158-->[com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:26:9-30:39
159            android:name="com.google.firebase.provider.FirebaseInitProvider"
159-->[com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:27:13-77
160            android:authorities="com.longint.lomag.lomagweb.firebaseinitprovider"
160-->[com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:28:13-72
161            android:exported="false"
161-->[com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:29:13-37
162            android:initOrder="100" />
162-->[com.google.firebase:firebase-common:19.3.0] C:\vs\gradle-5.6.4\caches\transforms-3\584ef3bea3b1bca282694de0f82f7b99\transformed\jetified-firebase-common-19.3.0\AndroidManifest.xml:30:13-36
163
164        <meta-data
164-->[com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:23:9-25:69
165            android:name="com.google.android.gms.version"
165-->[com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:24:13-58
166            android:value="@integer/google_play_services_version" />
166-->[com.google.android.gms:play-services-basement:17.0.0] C:\vs\gradle-5.6.4\caches\transforms-3\9b9ad494db3ac399c5bdafe7ea492396\transformed\jetified-play-services-basement-17.0.0\AndroidManifest.xml:25:13-66
167
168        <service
168-->[com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:29:9-35:19
169            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
169-->[com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:30:13-103
170            android:exported="false" >
170-->[com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:31:13-37
171            <meta-data
171-->[com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:32:13-34:39
172                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
172-->[com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:33:17-94
173                android:value="cct" />
173-->[com.google.android.datatransport:transport-backend-cct:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\2d53bcdc28aac0730eba23314aec10bf\transformed\jetified-transport-backend-cct-2.2.3\AndroidManifest.xml:34:17-36
174        </service>
175        <service
175-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:26:9-30:19
176            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
176-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:27:13-117
177            android:exported="false"
177-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:28:13-37
178            android:permission="android.permission.BIND_JOB_SERVICE" >
178-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:29:13-69
179        </service>
180
181        <receiver
181-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:32:9-34:40
182            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
182-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:33:13-132
183            android:exported="false" />
183-->[com.google.android.datatransport:transport-runtime:2.2.3] C:\vs\gradle-5.6.4\caches\transforms-3\d5b7179bc83aab635c0482dd8ebfb8ba\transformed\jetified-transport-runtime-2.2.3\AndroidManifest.xml:34:13-37
184    </application>
185
186</manifest>
