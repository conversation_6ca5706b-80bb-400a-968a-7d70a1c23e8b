package com.longint.lomag.lomagweb.db.procedures.add;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.longint.lomag.lomagweb.LomagApplication;
import com.longint.lomag.lomagweb.data.SelectedWarehouseData;
import com.longint.lomag.lomagweb.db.procedures.Procedure;
import com.longint.lomag.lomagweb.db.toobjects.DocumentElement;
import com.longint.lomag.lomagweb.db.toobjects.Param_Data;
import com.longint.lomag.lomagweb.db.toobjects.RentalDocumentLines;
import com.longint.lomag.lomagweb.db.toobjects.SerialNumber;
import com.longint.lomag.lomagweb.db.procedures.add.SaveSerialNumberDedicatedColumnsProcedure;
import com.longint.lomag.lomagweb.utils.Constants_Data;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

public class AddSaveReturnRentalDocumentElementProcedure implements Procedure {

    private enum ExecutionResult {
        UNKNOWN,
        OK,
        AMOUNT_IS_TOO_LARGE,
        SERIAL_NUMBER_ERROR,
        EXCEPTION_OCCURRED
    }
    private ExecutionResult executionResult;
    private boolean isAdded;
    private Context context;
    private PreparedStatement _serialStatement,_statement_param;
    private CallableStatement _statement;
    private RentalDocumentLines rentalDocumentLines;
    private int getInsertedDocumentId;
    private PreparedStatement _getIDStatement;
    private final String GET_INSERTED_RuchMagazynowy_STATEMENT = "SELECT TOP 1 * FROM RuchMagazynowy WHERE IDUzytkownika=? ORDER BY IDRuchuMagazynowego DESC";
    private Collection<SerialNumber> serialNrs;
    private static final String Call_SaveRentalDocumentElement_STATEMENT = "{call dbo.SaveRentalDocumentElement (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)}";
    private PreparedStatement _getSerialsStatement;
    private static final String SELECT_ITEMS_SERIAL_CODES = "SELECT  el.IDTowaru, sn.Number, max(sn.IDSerialNumber) as IDSerialNumber " +
            "FROM  SerialNumbers sn " +
            "INNER JOIN ElementRuchuMagazynowego el ON sn.IDElementuRuchuMagazynowego = el.IDElementuRuchuMagazynowego " +
            "INNER JOIN RuchMagazynowy r ON el.IDRuchuMagazynowego = r.IDRuchuMagazynowego " +
            "INNER JOIN RodzajRuchuMagazynowego o ON r.IDRodzajuRuchuMagazynowego = o.IDRodzajuRuchuMagazynowego " +
            "WHERE " +
            "el.IDElementuRuchuMagazynowego <> ? " +
            "GROUP BY el.IDTowaru, Number having SUM(r.Operator * sign(el.Ilosc)) > 0";

    private static final String INSERT_SERIAL_NR_STATEMENT = "INSERT INTO " + SerialNumber.TABLE_NAME + " VALUES (?,?)";
    private static final String INSERT_PARAMETERS_STATEMENT = "INSERT INTO Parameters (GUID, IntParam, DecParam) VALUES (?, ?, ?)";
    private static final String REMOVE_PARAMETERS_STATEMENT = "DELETE FROM Parameters WHERE (GUID=?)";

    private static final String INSERT_LOCATION = "UPDATE " + DocumentElement.TABLE_NAME +
            " SET " + DocumentElement.LOCATION_ID_NAME + "=? " +
            " WHERE " + DocumentElement.ID_NAME + "=?";

    private static final String SaveRentalCheckDocument = "SELECT DISTINCT rentEl.IDRuchuMagazynowego, rentDoc.IDRodzajuRuchuMagazynowego " +
            "FROM [dbo].[RentalElementsRelations] rel " +
            "INNER JOIN dbo.ElementRuchuMagazynowego retEl ON retEl.IDElementuRuchuMagazynowego = rel.IDReturnElement " +
            "INNER JOIN dbo.ElementRuchuMagazynowego rentEl ON rentEl.IDElementuRuchuMagazynowego = rel.IDRentElement " +
            "INNER JOIN dbo.RuchMagazynowy rentDoc ON rentDoc.IDRuchuMagazynowego = rentEl.IDRuchuMagazynowego " +
            "WHERE retEl.IDRuchuMagazynowego = ? ";

    private static final String SaveRentalCheckStatus = "IF (SELECT ISNULL(sum(ilosc), 0) from " +
            " (select IDElementuRuchuMagazynowego, ilosc FROM ElementRuchuMagazynowego WHERE IDRuchuMagazynowego = ? " +
            " UNION ALL " +
            " select rent.IDElementuRuchuMagazynowego, -sum(ret.ilosc) as ilosc from dbo.RentalElementsRelations rel " +
            "  inner join dbo.ElementRuchuMagazynowego rent ON rel.IDRentElement = rent.IDElementuRuchuMagazynowego " +
            "  inner join dbo.ElementRuchuMagazynowego ret ON rel.IDReturnElement = ret.IDElementuRuchuMagazynowego " +
            " where rent.IDRuchuMagazynowego = ? " +
            " group by rent.IDElementuRuchuMagazynowego " +
            " ) Q ) > 0 " +
            "SELECT 0 as [Status] " +
            "ELSE " +
            "SELECT 1 as [Status] ";

    private static final String SaveRentalUpdateStatus = " UPDATE dbo.RentalDocuments SET IDStatus = "+
            " (SELECT TOP 1 [IDRentalStatus] FROM [dbo].[RentalStatus] WHERE ([SetUpAction] & ?) <> 0) WHERE IDDocument = ? ";


    private void checkLength(RentalDocumentLines doc)
    {
        if (doc.get_Remarks().length()>2000)
            doc.set_Remarks(doc.get_Remarks().substring(0,1999));
    }


    public AddSaveReturnRentalDocumentElementProcedure(RentalDocumentLines document, Collection<SerialNumber> serialNrs, Context context) {
        executionResult = ExecutionResult.UNKNOWN;
        this.rentalDocumentLines = document;
        this.context = context;
        this.isAdded = SelectedWarehouseData.getInstance().isAdding();
        this.serialNrs=serialNrs;
        checkLength(rentalDocumentLines);
    }


    @Override
    public ResultSet executeProcedure(Connection connection) throws SQLException {

        //	if (DBConnectionClass.connection == null)// || !DBConnectionClass.connection.isValid(DBConnectionClass.FISRT_TIMEOUT))

        try {

            if (serialNrs != null)
            {

                Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure - serialNrs "+serialNrs.size());


                Set<Integer> itemsSet = new HashSet<Integer>();
                HashMap<String, Integer> itemsForSerials = new HashMap<String, Integer>();
                HashMap<Integer, Set<SerialNumber>> serialsForItems = new HashMap<Integer, Set<SerialNumber>>();

//                itemsSet=SelectedWarehouseData.getInstance().getItemsWithSerialsIds();
//               itemsForSerials=SelectedWarehouseData.getInstance().getItemsForSerials();
//                serialsForItems=SelectedWarehouseData.getInstance().getSerialsForItems();

				try
				{
					_getSerialsStatement = connection.prepareStatement(SELECT_ITEMS_SERIAL_CODES);
					_getSerialsStatement.setInt(1, -1);
					ResultSet rs = _getSerialsStatement.executeQuery();
					Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure - rs.getFetchSize() "+rs.getFetchSize());



					while (rs.next())
					{
						SerialNumber sn = new SerialNumber();
						String serial = rs.getString(SerialNumber.SERIAL_NR_NAME);
						int itemId = rs.getInt(DocumentElement.ID_ITEM_NAME);
						int serialId = rs.getInt(SerialNumber.ID_NAME);
						sn.setId(serialId);
						sn.setSerialNr(serial);
						itemsSet.add(itemId);
						itemsForSerials.put(serial.trim().toLowerCase(), itemId);
						if (serialsForItems.containsKey(itemId))
						{
							serialsForItems.get(itemId).add(sn);
						} else
						    {
							Set<SerialNumber> list = new HashSet<SerialNumber>();
							list.add(sn);
							serialsForItems.put(itemId, list);

						}
					}

					SelectedWarehouseData.getInstance().setItemsWithSerialsIds(itemsSet);
					SelectedWarehouseData.getInstance().setItemsForSerials(itemsForSerials);
					SelectedWarehouseData.getInstance().setSerialsForItems(serialsForItems);

				}
				catch (Exception e)
				{
					Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure - setSerialsForItems "+e.toString());
				}

//                boolean isGlobal = SelectedWarehouseData.getInstance().getSerialSupportMode().equals("Global");
//                Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure - isGlobal "+isGlobal);
//
//                try
//                {
//                    for (SerialNumber serialNumber : serialNrs)
//                    {
//                        boolean exists;
//
//                        if (isGlobal)
//                        {
//                            exists = itemsForSerials.containsKey(serialNumber.getSerialNr().trim().toLowerCase());
//                        } else {
//                            exists = serialsForItems.containsKey(rentalDocumentLines.get_idItem()) && serialsForItems.get(rentalDocumentLines.get_idItem()).contains(serialNumber);
//                        }
//                        if (isAdded && exists) {
//                            SelectedWarehouseData.getInstance().setProblemSerial(serialNumber);
//                            executionResult = ExecutionResult.SERIAL_NUMBER_ERROR;
//                            return null;
//                        }
//                        if (!isAdded && !exists) {
//                            SelectedWarehouseData.getInstance().setProblemSerial(serialNumber);
//                            executionResult = ExecutionResult.SERIAL_NUMBER_ERROR;
//                            return null;
//                        }
//                    }
//                }
//                catch (Exception e)
//                {
//                    Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure - SerialNumber "+e.toString());
//                }

            }

            try
            {

                ArrayList<Param_Data> param_dataArrayList=rentalDocumentLines.getParams();

                Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure - param_dataArrayList "+param_dataArrayList.size());
                for (int i = 0; i < param_dataArrayList.size(); i++)
                {
                    Param_Data param_data=param_dataArrayList.get(i);

                    _statement_param = connection.prepareStatement(INSERT_PARAMETERS_STATEMENT);
                    _statement_param.setString(1,rentalDocumentLines.getGuid());
                    _statement_param.setInt(2,Integer.parseInt(param_data.getIntParam()));
                    _statement_param.setDouble(3,Double.parseDouble(param_data.getDecParam()));
                    _statement_param.executeUpdate();
                }
            }
            catch (Exception e)
            {
                Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure - param_dataArrayList "+e.toString());
            }



            Log.e("AddSaveReturnRentalDocumentElementProcedure Call_SaveRentalDocumentElement_STATEMENT ",Call_SaveRentalDocumentElement_STATEMENT+"");
            _statement = connection.prepareCall(Call_SaveRentalDocumentElement_STATEMENT);

            Log.e("AddSaveReturnRentalDocumentElementProcedure get_id_Document ",rentalDocumentLines.get_id_Document()+"");

            Log.e("AddSaveReturnRentalDocumentElementProcedure get_idItem ",rentalDocumentLines.get_idItem()+"");
            Log.e("AddSaveReturnRentalDocumentElementProcedure get_idVat ",rentalDocumentLines.get_idVat()+"");

            _statement.registerOutParameter(1, Types.INTEGER);
            _statement.setInt(1, -1);
            _statement.setInt(2, rentalDocumentLines.get_id_Document());
            _statement.setInt(3, rentalDocumentLines.get_idItem());
            _statement.setDouble(4, rentalDocumentLines.get_Quantity());
            _statement.setDouble(5, rentalDocumentLines.get_net_price());
            _statement.setDouble(6, rentalDocumentLines.get_gross_price());

            if (rentalDocumentLines.get_idVat() > 0) {
                _statement.setInt(7, rentalDocumentLines.get_idVat());
            } else {
                _statement.setNull(7, Types.INTEGER);
            }

            _statement.setDouble(8, rentalDocumentLines.get_net_deposite());
            _statement.setDouble(9, rentalDocumentLines.get_gross_deposite());
           // _statement.setDouble(8, rentalDocumentLines.get_net_amount());
           // _statement.setDouble(9, rentalDocumentLines.get_gross_amount());

            if (rentalDocumentLines.get_idRentalUnit()<=0)
                _statement.setNull(10,Types.INTEGER);
            else
                _statement.setInt(10, rentalDocumentLines.get_idRentalUnit());

            if (TextUtils.isEmpty(rentalDocumentLines.get_Remarks()))
                _statement.setString(11,"");
            else
                _statement.setString(11, rentalDocumentLines.get_Remarks());

            if (rentalDocumentLines.get_idUser()<=0)
                _statement.setNull(12,Types.INTEGER);
            else
                _statement.setInt(12, rentalDocumentLines.get_idUser());

            Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure - get_idWarehouseLocation "+rentalDocumentLines.get_idWarehouseLocation());

            if (rentalDocumentLines.get_idWarehouseLocation()<=0)
                _statement.setNull(13,Types.INTEGER);
            else
                _statement.setInt(13, rentalDocumentLines.get_idWarehouseLocation());

            _statement.setString(14, null);

            if (TextUtils.isEmpty(rentalDocumentLines.getGuid()))
                _statement.setString(15, null);
            else
                _statement.setString(15, rentalDocumentLines.getGuid());


            _statement.executeUpdate();

            getInsertedDocumentId= _statement.getInt(1);

            Log.e("AddSaveReturnRentalDocumentElementProcedure registerOutParameter id_getInsertedElement ",getInsertedDocumentId+"");

            _statement_param = connection.prepareStatement(REMOVE_PARAMETERS_STATEMENT);
            _statement_param.setString(1,rentalDocumentLines.getGuid());
            _statement_param.executeUpdate();

            insertSerialNrs(connection, serialNrs, getInsertedDocumentId);

            //ustawienie statusu
            int idDocument = rentalDocumentLines.get_id_Document();
            int IDRuchuMagazynowego = 0;
            PreparedStatement _statementParam = connection.prepareStatement(SaveRentalCheckDocument);
            _statementParam.setInt(1, idDocument);

            ResultSet resultDelivery = _statementParam.executeQuery();
            if (resultDelivery != null) {
                while (resultDelivery.next()) {
                    IDRuchuMagazynowego = resultDelivery.getInt("IDRuchuMagazynowego");
                    break;
                }
                resultDelivery.close();
            }

            if (IDRuchuMagazynowego > 0) {

                _statementParam = connection.prepareStatement(SaveRentalCheckStatus);
                _statementParam.setInt(1, IDRuchuMagazynowego);
                _statementParam.setInt(2, IDRuchuMagazynowego);

                double status = 0;
                resultDelivery = _statementParam.executeQuery();
                if (resultDelivery != null) {
                    while (resultDelivery.next()) {
                        status = resultDelivery.getDouble("Status");
                        break;
                    }
                    resultDelivery.close();
                }

                if (status == 1) {
                    _statementParam = connection.prepareStatement(SaveRentalUpdateStatus);
                    _statementParam.setInt(1, 8);
                    _statementParam.setInt(2, IDRuchuMagazynowego);
                    _statementParam.executeUpdate();
                }
            }

            //updateUser(connection, _statement.getInt(7), document.getIdUser());

            //document.setId(_statement.getInt(7));
            executionResult = ExecutionResult.OK;
        }
        catch (SQLException e)
        {
            if (e.getMessage().equals("" + rentalDocumentLines.get_idItem()))
            {
                executionResult = ExecutionResult.AMOUNT_IS_TOO_LARGE;
                Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure - executionResult "+executionResult);

            }
            else {
                executionResult = ExecutionResult.EXCEPTION_OCCURRED;
                Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure - executionResult "+executionResult);
                throw e;
            }
        }
        catch (Exception e)
        {
            Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure - Exception "+e.toString());
        }
        finally
        {
            if (_statement != null)
                _statement.close();

            Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure - finished");
        }

        return null;
    }

    public boolean isAmountTooLarge() {
        return executionResult == ExecutionResult.AMOUNT_IS_TOO_LARGE;
    }

    public boolean serialNumberErrorOccurred() {
        return executionResult == ExecutionResult.SERIAL_NUMBER_ERROR;
    }

    public int getGetInsertedDocumentId() {
        return getInsertedDocumentId;
    }

    public void setGetInsertedDocumentId(int getInsertedDocumentId) {
        this.getInsertedDocumentId = getInsertedDocumentId;
    }

    private void updateUser(Connection connection, int warehouseMoveId, int user_id) throws SQLException {
        PreparedStatement statement = connection.prepareStatement(
                "update RuchMagazynowy set IDUzytkownika = ? where IDRuchuMagazynowego = ?;"
        );
        statement.setInt(1, user_id);
        statement.setInt(2, warehouseMoveId);
        statement.executeUpdate();

        Log.e("AddSaveReturnRentalDocumentElementProcedure updateUser user.getId() ",user_id+"");

        int id_getInsertedElement=  getInsertedElementId(connection,rentalDocumentLines.get_idUser());

        Log.e("AddSaveReturnRentalDocumentElementProcedure getInsertedElementId id_getInsertedElement ",id_getInsertedElement+"");

        setGetInsertedDocumentId(id_getInsertedElement);
    }


    private void insertSerialNrs(
            Connection connection, Collection<SerialNumber> serialNrs, int id) throws SQLException {

        try
        {
            if (serialNrs != null) {

                for (SerialNumber serial : serialNrs)
                {

                    Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure serial.getSerialNr() "+serial.getSerialNr());

                    _serialStatement = connection.prepareStatement(INSERT_SERIAL_NR_STATEMENT);
                    _serialStatement.setInt(1, id);
                    _serialStatement.setString(2, serial.getSerialNr());
                    _serialStatement.executeUpdate();

                    // Insert dedicated columns data if available - use SaveSerialNumberDedicatedColumnsProcedure
                    if (serial.getDedicatedColumnsData() != null && !serial.getDedicatedColumnsData().isEmpty()) {
                        // Get item ID from document element
                        PreparedStatement getItemIdStatement = connection.prepareStatement(
                            "SELECT IDTowaru FROM dbo.ElementRuchuMagazynowego WHERE IDElementuRuchuMagazynowego = ?");
                        getItemIdStatement.setInt(1, id);
                        ResultSet itemRs = getItemIdStatement.executeQuery();

                        if (itemRs.next()) {
                            int itemId = itemRs.getInt("IDTowaru");

                            // Use the existing SaveSerialNumberDedicatedColumnsProcedure
                            SaveSerialNumberDedicatedColumnsProcedure dcProcedure = new SaveSerialNumberDedicatedColumnsProcedure(itemId, serial);
                            dcProcedure.executeProcedure(connection);
                        }
                    }
                }
            }
        }
        catch (Exception e)
        {
            Log.e(LomagApplication.APP_TAG, "AddSaveReturnRentalDocumentElementProcedure insertSerialNrs "+e.toString());
        }

    }
    private int getInsertedElementId(Connection connection,int USER_ID) throws SQLException
    {
        _getIDStatement = connection.prepareStatement(GET_INSERTED_RuchMagazynowy_STATEMENT);
        _getIDStatement.setInt(1, USER_ID);
        ResultSet rs = _getIDStatement.executeQuery();
        int id = 0;
        if (rs.next())
        {
            id = rs.getInt("IDRuchuMagazynowego");
        }
        return id;
    }

}
