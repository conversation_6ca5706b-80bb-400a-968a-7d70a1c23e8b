package com.longint.lomag.lomagweb.db.procedures.add;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.longint.lomag.lomagweb.LomagApplication;
import com.longint.lomag.lomagweb.data.SelectedWarehouseData;
import com.longint.lomag.lomagweb.db.procedures.Procedure;
import com.longint.lomag.lomagweb.db.toobjects.DocumentElement;
import com.longint.lomag.lomagweb.db.toobjects.DocumentType;
import com.longint.lomag.lomagweb.db.toobjects.RentalDocumentLines;
import com.longint.lomag.lomagweb.db.toobjects.SerialNumber;
import com.longint.lomag.lomagweb.db.procedures.add.SaveSerialNumberDedicatedColumnsProcedure;
import com.longint.lomag.lomagweb.utils.Constants_Data;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public class AddSaveRentalDocumentElementProcedure implements Procedure {

	private enum ExecutionResult {
		UNKNOWN,
		OK,
		AMOUNT_IS_TOO_LARGE,
		SERIAL_NUMBER_ERROR,
		EXCEPTION_OCCURRED
	}
	private ExecutionResult executionResult;
	private PreparedStatement _statementParam;
	private boolean isAdded;
	private Context context;
	private PreparedStatement _serialStatement;
	private CallableStatement _statement;
	private RentalDocumentLines rentalDocumentLines;
	private int getInsertedDocumentId;
	private PreparedStatement _getIDStatement;
	private final String GET_INSERTED_RuchMagazynowy_STATEMENT = "SELECT TOP 1 * FROM RuchMagazynowy WHERE IDUzytkownika=? ORDER BY IDRuchuMagazynowego DESC";
	private Collection<SerialNumber> serialNrs;
	private static final String Call_SaveRentalDocumentElement_STATEMENT = "{call dbo.SaveRentalDocumentElement (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)}";
	private PreparedStatement _getSerialsStatement;
	private static final String SELECT_ITEMS_SERIAL_CODES = "SELECT  el.IDTowaru, sn.Number, max(sn.IDSerialNumber) as IDSerialNumber " +
			"FROM  SerialNumbers sn " +
			"INNER JOIN ElementRuchuMagazynowego el ON sn.IDElementuRuchuMagazynowego = el.IDElementuRuchuMagazynowego " +
			"INNER JOIN RuchMagazynowy r ON el.IDRuchuMagazynowego = r.IDRuchuMagazynowego " +
			"INNER JOIN RodzajRuchuMagazynowego o ON r.IDRodzajuRuchuMagazynowego = o.IDRodzajuRuchuMagazynowego " +
			"WHERE " +
			"el.IDElementuRuchuMagazynowego <> ? " +
			"GROUP BY el.IDTowaru, Number having SUM(r.Operator * sign(el.Ilosc)) > 0";

	private static final String INSERT_SERIAL_NR_STATEMENT = "INSERT INTO " + SerialNumber.TABLE_NAME + " VALUES (?,?)";

	private static final String INSERT_LOCATION = "UPDATE " + DocumentElement.TABLE_NAME +
			" SET " + DocumentElement.LOCATION_ID_NAME + "=? " +
			" WHERE " + DocumentElement.ID_NAME + "=?";

	private static final String UPDATE_PICKING_STATUS_STATEMENT = " select case when amount = 0 then 100 else (realized / amount) * 100 end as [Status] from (\n" +
			"select sum(ol.quantity) as amount, sum(dbo.OrderLineRealization(ol.IDOrderLine)) as realized \n" +
			"from orderlines ol inner join Towar t ON t.IDTowaru = ol.IDItem \n" +
			"WHERE ol.IDOrder = ? AND t.Usluga = 0 \n" +
			") Q ";

	private static final String UPDATE_PICKING_STATUS_SET_STATEMENT = " UPDATE [Orders] SET IDOrderStatus = (SELECT TOP 1 IDOrderStatus FROM dbo.OrderStatus WHERE (SetUpAction & ?) > 0) WHERE IDOrder = ? ";

	private static final String INSERT_PARAMETERS_STATEMENT = "INSERT INTO Parameters (GUID, IntParam, DecParam) VALUES (?, ?, ?) Select CAST (SCOPE_IDENTITY() AS INT)";
	private static final String REMOVE_PARAMETERS_STATEMENT = "DELETE FROM Parameters WHERE (GUID=?)";

	private void checkLength(RentalDocumentLines doc)
	{
		if (doc.get_Remarks().length()>2000)
			doc.set_Remarks(doc.get_Remarks().substring(0,1999));
	}


	public AddSaveRentalDocumentElementProcedure(RentalDocumentLines document, Collection<SerialNumber> serialNrs, Context context) {
		executionResult = ExecutionResult.UNKNOWN;
		this.rentalDocumentLines = document;
		this.context = context;
		this.isAdded = SelectedWarehouseData.getInstance().isAdding();
		this.serialNrs=serialNrs;
		checkLength(rentalDocumentLines);
	}


	@Override
	public ResultSet executeProcedure(Connection connection) throws SQLException {

		//	if (DBConnectionClass.connection == null)// || !DBConnectionClass.connection.isValid(DBConnectionClass.FISRT_TIMEOUT))

		try {

			if (serialNrs != null)
			{

				Log.e(LomagApplication.APP_TAG, "AddSaveRentalDocumentElementProcedure - serialNrs "+serialNrs.size());

				_getSerialsStatement = connection.prepareStatement(SELECT_ITEMS_SERIAL_CODES);
				_getSerialsStatement.setInt(1, -1);
				ResultSet rs = _getSerialsStatement.executeQuery();
				Log.e(LomagApplication.APP_TAG, "AddSaveRentalDocumentElementProcedure - rs.getFetchSize() "+rs.getFetchSize());

				HashSet<Integer> itemsSet = new HashSet<Integer>();
				HashMap<String, Integer> itemsForSerials = new HashMap<String, Integer>();
				HashMap<Integer, Set<SerialNumber>> serialsForItems = new HashMap<Integer, Set<SerialNumber>>();
				while (rs.next()) {
					SerialNumber sn = new SerialNumber();
					String serial = rs.getString(SerialNumber.SERIAL_NR_NAME);
					int itemId = rs.getInt(DocumentElement.ID_ITEM_NAME);
					int serialId = rs.getInt(SerialNumber.ID_NAME);
					sn.setId(serialId);
					sn.setSerialNr(serial);
					itemsSet.add(itemId);
					itemsForSerials.put(serial.trim().toLowerCase(), itemId);
					if (serialsForItems.containsKey(itemId))
					{
						serialsForItems.get(itemId).add(sn);
					} else {
						Set<SerialNumber> list = new HashSet<SerialNumber>();
						list.add(sn);
						serialsForItems.put(itemId, list);

					}
				}

				SelectedWarehouseData.getInstance().setItemsWithSerialsIds(itemsSet);
				SelectedWarehouseData.getInstance().setItemsForSerials(itemsForSerials);
				SelectedWarehouseData.getInstance().setSerialsForItems(serialsForItems);

				boolean isGlobal = SelectedWarehouseData.getInstance().getSerialSupportMode().equals("Global");

				try
				{
					for (SerialNumber serialNumber : serialNrs)
					{
						boolean exists;
						if (isGlobal)
						{
							exists = itemsForSerials.containsKey(serialNumber.getSerialNr().trim().toLowerCase());
						} else
						{
							exists = serialsForItems.containsKey(rentalDocumentLines.get_idItem()) && serialsForItems.get(rentalDocumentLines.get_idItem()).contains(serialNumber);
						}
						if (isAdded && exists)
						{
							SelectedWarehouseData.getInstance().setProblemSerial(serialNumber);
							executionResult = ExecutionResult.SERIAL_NUMBER_ERROR;
							return null;
						}
						if (!isAdded && !exists)
						{
							SelectedWarehouseData.getInstance().setProblemSerial(serialNumber);
							executionResult = ExecutionResult.SERIAL_NUMBER_ERROR;
							return null;
						}
					}
				}
				catch (Exception e)
				{
					Log.e(LomagApplication.APP_TAG, "AddSaveRentalDocumentElementProcedure - SerialNumber "+e.toString());
				}

			}

			int docId = SelectedWarehouseData.getInstance().getCurrentDocType().getId();
			int modePicking = SelectedWarehouseData.getInstance().getModeWzPicking();
			int docWzPickingId = SelectedWarehouseData.getInstance().getDocWzPicking();
			if (docId == DocumentType.RENTAL_ID && modePicking == SelectedWarehouseData.MODE_WY_PICKING) {

				boolean quantityFromDelivery = true;
				String guid = UUID.randomUUID().toString();
				Double iloscSuma = 0.0;
				Double iloscSn = 0.0;
				List<DocumentElement> elementy = rentalDocumentLines.getDocumentElement().getItemDelivery();
				if (elementy == null || elementy.size() == 0)
				{
					elementy = new ArrayList<>();
					elementy.add(rentalDocumentLines.getDocumentElement());
					rentalDocumentLines.getDocumentElement().setItemDelivery(elementy);
					quantityFromDelivery = false;
				}

				for (DocumentElement elem : rentalDocumentLines.getDocumentElement().getItemDelivery()) {
					if (quantityFromDelivery) { quantityFromDelivery = false; }
					guid = UUID.randomUUID().toString();
					int id = 0;
					double ilosc = 0;
					iloscSuma = 0.0;
					try {
						ilosc = elem.getQuantityEdit();
						id = elem.getId();
						if (ilosc > 0 && id > 0) {
							quantityFromDelivery = true;
							iloscSuma += ilosc;
							_statementParam = connection.prepareStatement(INSERT_PARAMETERS_STATEMENT);
							_statementParam.setString(1, guid);
							_statementParam.setInt(2, id);
							_statementParam.setBigDecimal(3, new BigDecimal(ilosc));
							_statementParam.executeUpdate();
						}
					} catch (Exception e) {
						Log.e(LomagApplication.APP_TAG, "AddDocumentElementProcedure insertParameters " + e.toString());
						throw e;
					}

					Log.e("AddSaveRentalDocumentElementProcedure Call_SaveRentalDocumentElement_STATEMENT ",Call_SaveRentalDocumentElement_STATEMENT+"");
					_statement = connection.prepareCall(Call_SaveRentalDocumentElement_STATEMENT);

	/*
	declare @p3 int
set @p3=1070
exec sp_executesql N'EXECUTE [dbo].[SaveRentalDocumentElement] @ElementID OUTPUT,
@DocumentID,
@ItemID,
@Amount,
@PriceNet,
@PriceGross,
@VatID,
@Discount,
@DiscountID,
@DepositNet,
@DepositGross,
@RentUnitID,
@Remarks,
@UserID,
@LocationID,
@SnGuidParam,
@DeliveryGuid',N'@ElementID int output,@DocumentID int,@ItemID int,@Amount decimal(1,0),@PriceNet decimal(1,0),@PriceGross decimal(1,0),@VatID int,@Discount decimal(1,0),@DiscountID nvarchar(4000),@DepositNet decimal(1,0),@DepositGross decimal(1,0),@RentUnitID int,@Remarks nvarchar(4000),@UserID int,@SnGuidParam nvarchar(4000),@LocationID nvarchar(4000),@DeliveryGuid nvarchar(36)',
@ElementID=@p3 output,@DocumentID=177,@ItemID=2,@Amount=1,@PriceNet=0,@PriceGross=0,@VatID=1,@Discount=0,@DiscountID=NULL,@DepositNet=0,@DepositGross=0,@RentUnitID=2,@Remarks=NULL,@UserID=1,@SnGuidParam=NULL,@LocationID=NULL,@DeliveryGuid=N'b36283fd-fa0b-4093-bc2b-11718ea56b01'
select @p3
	 */


					_statement.registerOutParameter(1, Types.INTEGER);
					_statement.setInt(1, -1);
					_statement.setInt(2, rentalDocumentLines.get_id_Document());
					_statement.setInt(3, rentalDocumentLines.get_idItem());
					//_statement.setDouble(4, rentalDocumentLines.get_Quantity());
					_statement.setBigDecimal(4,  new BigDecimal(quantityFromDelivery ? iloscSuma : rentalDocumentLines.get_Quantity()));
					_statement.setDouble(5, rentalDocumentLines.get_net_price());
					_statement.setDouble(6, rentalDocumentLines.get_gross_price());
					_statement.setDouble(7, rentalDocumentLines.get_idVat());
					_statement.setDouble(8, rentalDocumentLines.get_net_deposite());
					_statement.setDouble(9, rentalDocumentLines.get_gross_deposite());

					if (rentalDocumentLines.get_idRentalUnit()<=0)
						_statement.setNull(10,Types.INTEGER);
					else
						_statement.setInt(10, rentalDocumentLines.get_idRentalUnit());

					if (TextUtils.isEmpty(rentalDocumentLines.get_Remarks()))
						_statement.setString(11,"");
					else
						_statement.setString(11, rentalDocumentLines.get_Remarks());

					if (rentalDocumentLines.get_idUser()<=0)
						_statement.setNull(12,Types.INTEGER);
					else
						_statement.setInt(12, rentalDocumentLines.get_idUser());

					if (rentalDocumentLines.get_idWarehouseLocation()<=0)
						_statement.setNull(13,Types.INTEGER);
					else
						_statement.setInt(13, rentalDocumentLines.get_idWarehouseLocation());

					_statement.setString(14, null);

					/*if (TextUtils.isEmpty(rentalDocumentLines.getGuid()))
						_statement.setString(15, null);
					else*/

					if (quantityFromDelivery)
					{
						_statement.setString(15, guid);
					}
					else
					{
						_statement.setString(15, null);
					}

					_statement.executeUpdate();
					getInsertedDocumentId = _statement.getInt(1);

					if (quantityFromDelivery) {
						try {
							_statementParam = connection.prepareStatement(REMOVE_PARAMETERS_STATEMENT);
							_statementParam.setString(1, guid);
							_statementParam.executeUpdate();
						} catch (Exception e) {
							Log.e(LomagApplication.APP_TAG, "AddDocumentElementProcedure deleteParameters " + e.toString());
							throw e;
						}
					}
				}
			}
			else
			{
				Log.e("AddSaveRentalDocumentElementProcedure Call_SaveRentalDocumentElement_STATEMENT ",Call_SaveRentalDocumentElement_STATEMENT+"");
				_statement = connection.prepareCall(Call_SaveRentalDocumentElement_STATEMENT);

				_statement.registerOutParameter(1, Types.INTEGER);
				_statement.setInt(1, -1);
				_statement.setInt(2, rentalDocumentLines.get_id_Document());
				_statement.setInt(3, rentalDocumentLines.get_idItem());
				_statement.setBigDecimal(4, new BigDecimal(rentalDocumentLines.get_Quantity()));
				_statement.setDouble(5, rentalDocumentLines.get_net_price());
				_statement.setDouble(6, rentalDocumentLines.get_gross_price());
				_statement.setDouble(7, rentalDocumentLines.get_idVat());
				_statement.setDouble(8, rentalDocumentLines.get_net_deposite());
				_statement.setDouble(9, rentalDocumentLines.get_gross_deposite());

				if (rentalDocumentLines.get_idRentalUnit()<=0)
					_statement.setNull(10,Types.INTEGER);
				else
					_statement.setInt(10, rentalDocumentLines.get_idRentalUnit());

				if (TextUtils.isEmpty(rentalDocumentLines.get_Remarks()))
					_statement.setString(11,"");
				else
					_statement.setString(11, rentalDocumentLines.get_Remarks());

				if (rentalDocumentLines.get_idUser()<=0)
					_statement.setNull(12,Types.INTEGER);
				else
					_statement.setInt(12, rentalDocumentLines.get_idUser());

				if (rentalDocumentLines.get_idWarehouseLocation()<=0)
					_statement.setNull(13,Types.INTEGER);
				else
					_statement.setInt(13, rentalDocumentLines.get_idWarehouseLocation());

				_statement.setString(14, null);

				if (TextUtils.isEmpty(rentalDocumentLines.getGuid()))
					_statement.setString(15, null);
				else
					_statement.setString(15, rentalDocumentLines.getGuid());

				_statement.executeUpdate();
				getInsertedDocumentId = _statement.getInt(1);
			}

			Log.e("AddSaveRentalDocumentElementProcedure registerOutParameter id_getInsertedElement ",getInsertedDocumentId+"");

			insertSerialNrs(connection, serialNrs, getInsertedDocumentId);

			//updateUser(connection, _statement.getInt(7), document.getIdUser());


			//ustawienie statusu zamówienia
			if (((docId == DocumentType.RELEASE_ID && modePicking == SelectedWarehouseData.MODE_WZ_PICKING) ||
					(docId == DocumentType.RENTAL_ID && modePicking == SelectedWarehouseData.MODE_WY_PICKING)) && docWzPickingId > 0) {

				_statementParam = connection.prepareStatement(UPDATE_PICKING_STATUS_STATEMENT);
				_statementParam.setInt(1,docWzPickingId);

				double status = 0;
				ResultSet resultDelivery = _statementParam.executeQuery();
				if (resultDelivery != null) {
					while (resultDelivery.next()) {
						status = resultDelivery.getDouble("Status");
						break;
					}
				}

				if (status >= 100)
				{
					_statementParam = connection.prepareStatement(UPDATE_PICKING_STATUS_SET_STATEMENT);
					_statementParam.setInt(1, 4);
					_statementParam.setInt(2, docWzPickingId);
					_statementParam.executeUpdate();
				}
				else
				{
					_statementParam = connection.prepareStatement(UPDATE_PICKING_STATUS_SET_STATEMENT);
					_statementParam.setInt(1, 2);
					_statementParam.setInt(2, docWzPickingId);
					_statementParam.executeUpdate();
				}
			}


			//document.setId(_statement.getInt(7));
			executionResult = ExecutionResult.OK;
		}
		catch (SQLException e)
		{
			if (e.getMessage().equals("" + rentalDocumentLines.get_idItem()))
			{
				executionResult = ExecutionResult.AMOUNT_IS_TOO_LARGE;
			}
			else {
				executionResult = ExecutionResult.EXCEPTION_OCCURRED;
				throw e;
			}
		} finally {
			if (_statement != null)
				_statement.close();

			Log.e(LomagApplication.APP_TAG, "AddSaveRentalDocumentElementProcedure - finished");
		}

		return null;
	}

	public boolean isAmountTooLarge() {
		return executionResult == ExecutionResult.AMOUNT_IS_TOO_LARGE;
	}

	public boolean serialNumberErrorOccurred() {
		return executionResult == ExecutionResult.SERIAL_NUMBER_ERROR;
	}

	public int getGetInsertedDocumentId() {
		return getInsertedDocumentId;
	}

	public void setGetInsertedDocumentId(int getInsertedDocumentId) {
		this.getInsertedDocumentId = getInsertedDocumentId;
	}

	private void updateUser(Connection connection, int warehouseMoveId, int user_id) throws SQLException {
		PreparedStatement statement = connection.prepareStatement(
				"update RuchMagazynowy set IDUzytkownika = ? where IDRuchuMagazynowego = ?;"
		);
		statement.setInt(1, user_id);
		statement.setInt(2, warehouseMoveId);
		statement.executeUpdate();

		Log.e("AddSaveRentalDocumentElementProcedure updateUser user.getId() ",user_id+"");

		int id_getInsertedElement=  getInsertedElementId(connection,rentalDocumentLines.get_idUser());

		Log.e("AddSaveRentalDocumentElementProcedure getInsertedElementId id_getInsertedElement ",id_getInsertedElement+"");

		setGetInsertedDocumentId(id_getInsertedElement);
	}


	private void insertSerialNrs(
			Connection connection, Collection<SerialNumber> serialNrs, int id) throws SQLException {

		try
		{
			if (serialNrs != null) {

				for (SerialNumber serial : serialNrs)
				{

					Log.e(LomagApplication.APP_TAG, "AddSaveRentalDocumentElementProcedure serial.getSerialNr() "+serial.getSerialNr());

					_serialStatement = connection.prepareStatement(INSERT_SERIAL_NR_STATEMENT);
					_serialStatement.setInt(1, id);
					_serialStatement.setString(2, serial.getSerialNr());
					_serialStatement.executeUpdate();

					// Insert dedicated columns data if available - use SaveSerialNumberDedicatedColumnsProcedure
					if (serial.getDedicatedColumnsData() != null && !serial.getDedicatedColumnsData().isEmpty()) {
						// Get item ID from document element
						PreparedStatement getItemIdStatement = connection.prepareStatement(
							"SELECT IDTowaru FROM dbo.ElementRuchuMagazynowego WHERE IDElementuRuchuMagazynowego = ?");
						getItemIdStatement.setInt(1, id);
						ResultSet itemRs = getItemIdStatement.executeQuery();

						if (itemRs.next()) {
							int itemId = itemRs.getInt("IDTowaru");

							// Use the existing SaveSerialNumberDedicatedColumnsProcedure
							SaveSerialNumberDedicatedColumnsProcedure dcProcedure = new SaveSerialNumberDedicatedColumnsProcedure(itemId, serial);
							dcProcedure.executeProcedure(connection);
						}
					}
				}
			}
		}
		catch (Exception e)
		{
			Log.e(LomagApplication.APP_TAG, "AddSaveRentalDocumentElementProcedure insertSerialNrs "+e.toString());
		}

	}
	private int getInsertedElementId(Connection connection,int USER_ID) throws SQLException
	{
		_getIDStatement = connection.prepareStatement(GET_INSERTED_RuchMagazynowy_STATEMENT);
		_getIDStatement.setInt(1, USER_ID);
		ResultSet rs = _getIDStatement.executeQuery();
		int id = 0;
		if (rs.next())
		{
			id = rs.getInt("IDRuchuMagazynowego");
		}
		return id;
	}

}
